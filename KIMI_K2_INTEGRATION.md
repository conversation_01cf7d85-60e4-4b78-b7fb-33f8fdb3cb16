# Kimi K2 Grading Integration through Groq

This document explains how to use the Kimi K2 model through Groq for grading in the VAST system.

## Overview

The system has been updated to support Kimi K2 (`moonshotai/kimi-k2-instruct`) through the Groq API as the primary grading model, with automatic fallback to Gemini when Groq/Kimi K2 is not available.

## Features

- **Primary Grading**: Uses Kimi K2 model (`moonshotai/kimi-k2-instruct`) through Groq for evaluating student answers
- **Automatic Fallback**: Falls back to Gemini when Groq/Kimi K2 is unavailable
- **Same Interface**: No changes to existing grading endpoints or functionality
- **Parallel Processing**: Maintains parallel processing of marking points for performance
- **Chemistry-Specific**: Uses chemistry-specific system prompts for better domain accuracy

## Setup

### 1. Ensure Groq API Key

The system uses the existing Groq API key from your `.env` file:

```bash
GROQ_API_KEY=your_groq_api_key_here
```

### 2. Restart Application

Restart the Flask application to load the new configuration:

```bash
python app.py
```

### 3. Verify Integration

Run the demo script to verify the integration:

```bash
python3 demo_kimi_integration.py
```

## How It Works

### Grading Process

1. **Client Initialization**: The system uses the existing Groq client on startup
2. **Grading Request**: When a grading request is made, the system checks if Groq client is available
3. **Model Selection**:
   - If Groq is available → Uses Kimi K2 through Groq
   - If Groq is unavailable → Falls back to Gemini
4. **Response Processing**: Processes the response using the same format for both models

### Kimi K2 Configuration

The Kimi K2 model through Groq uses the following configuration:

```python
completion = groq_client.chat.completions.create(
    model="moonshotai/kimi-k2-instruct",
    messages=[
        {"role": "system", "content": "You are an expert examiner evaluating a student's answer against a specific marking point for high school level chemistry questions"},
        {"role": "user", "content": grading_prompt}
    ],
    temperature=0.1,
)
```

## Modified Files

### `app.py`
- Removed separate Kimi client initialization
- Uses existing Groq client for Kimi K2 access

### `routes/api.py`
- Added `is_kimi_k2_available()` helper function
- Added `_process_single_marking_point_kimi()` function using Groq
- Added `_calculate_score_and_evaluated_points_kimi()` function using Groq
- Updated all grading endpoints to use Kimi K2 through Groq with Gemini fallback

## Grading Endpoints

The following endpoints now use Kimi K2 (with Gemini fallback):

1. **Submit Answer**: `/submit_answer/<question_id>/<part_id>`
2. **Get Diff**: `/get_diff/<question_id>/<part_id>`
3. **Highlighted Answer**: `/highlighted_answer/<question_id>/<part_id>`
4. **Submit Problemset**: `/submit_problemset/<problemset_id>`

## Testing

### Test Scripts

1. **Basic Integration Test**: `test_kimi_grading.py`
2. **Demo with Fallback**: `demo_kimi_integration.py`

### Running Tests

```bash
# Test Kimi K2 connection (requires API key)
python3 test_kimi_grading.py

# Demo the integration with fallback
python3 demo_kimi_integration.py
```

## Monitoring

The system logs which model is being used for each grading request:

- `"Using Kimi K2 for grading"` - When Kimi K2 through Groq is used
- `"Falling back to Gemini for grading (Kimi K2 not available)"` - When fallback occurs

## Troubleshooting

### Common Issues

1. **Groq API Key Not Found**
   - Ensure `GROQ_API_KEY` is in your `.env` file
   - Restart the application after adding the key

2. **Quota Exceeded**
   - Check your Groq API quota and billing
   - System will automatically fall back to Gemini

3. **Model Not Available**
   - Verify that `moonshotai/kimi-k2-instruct` is available in your Groq plan
   - Check Groq documentation for model availability

4. **Connection Issues**
   - Verify internet connectivity
   - Check if the Groq API endpoint is accessible

### Logs

Check the application logs for grading model selection:

```bash
tail -f logs/app.log | grep -E "(Kimi|Gemini|grading)"
```

## Performance

- **Parallel Processing**: Both Kimi K2 and Gemini use parallel processing for multiple marking points
- **Response Time**: Kimi K2 response times may vary; fallback ensures system reliability
- **Caching**: Consider implementing response caching for frequently graded content

## Future Enhancements

- **Model Selection UI**: Admin interface to choose between models
- **A/B Testing**: Compare grading quality between models
- **Response Caching**: Cache grading responses for identical answers
- **Batch Processing**: Process multiple submissions in batches
