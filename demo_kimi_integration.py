#!/usr/bin/env python3
"""
Demo script showing Kimi K2 integration with fallback to Gemini
"""

import os
from dotenv import load_dotenv
from groq import Groq
import google.generativeai as genai

# Load environment variables
load_dotenv()

def demo_grading_system():
    """Demonstrate the grading system with Kimi K2 through Groq and Gemini fallback"""

    print("🎯 Grading System Integration Demo")
    print("=" * 50)

    # Check Kimi K2 availability through Groq
    groq_api_key = os.getenv("GROQ_API_KEY")
    groq_client = None

    if groq_api_key:
        try:
            groq_client = Groq(api_key=groq_api_key)
            print("✅ Groq client initialized successfully (Kimi K2 available)")
        except Exception as e:
            print(f"❌ Failed to initialize Groq client: {e}")
            groq_client = None
    else:
        print("⚠️ GROQ_API_KEY not found - will use Gemini fallback")
    
    # Initialize Gemini as fallback
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    gemini_client = None
    
    if gemini_api_key:
        try:
            genai.configure(api_key=gemini_api_key)
            gemini_client = genai.GenerativeModel('gemini-2.5-flash')
            print("✅ Gemini client initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize Gemini client: {e}")
    else:
        print("❌ GEMINI_API_KEY not found")
    
    print("\n" + "=" * 50)
    
    # Demo grading prompt
    sample_prompt = """You are an expert examiner evaluating a student's answer against a specific marking point.

RESPONSE FORMAT:
You must respond in one of these 3 formats ONLY:

Format 1 - If the marking point is FULLY addressed:
YES
EVIDENCE: <exact text snippet>

Format 2 - If the marking point was PARTIALLY addressed:
PARTIAL
EVIDENCE: <exact text snippet>

Format 3 - If the marking point is NOT addressed:
NO

MARKING POINT: Explain the concept of gravity
STUDENT'S ANSWER: Gravity is a force that pulls objects toward the center of the Earth. It's what makes things fall down when you drop them."""

    # Test grading with available client
    if groq_client:
        print("\n🚀 Testing with Kimi K2 through Groq...")
        try:
            completion = groq_client.chat.completions.create(
                model="moonshotai/kimi-k2-instruct",
                messages=[
                    {"role": "system", "content": "You are an expert examiner evaluating a student's answer against a specific marking point for high school level chemistry questions"},
                    {"role": "user", "content": sample_prompt}
                ],
                temperature=0.1,
            )

            response = completion.choices[0].message.content
            print(f"✅ Kimi K2 Response:\n{response}")

        except Exception as e:
            print(f"❌ Kimi K2 through Groq failed: {e}")
            print("🔄 Falling back to Gemini...")
            groq_client = None  # Force fallback
    
    if not groq_client and gemini_client:
        print("\n🔄 Using Gemini fallback...")
        try:
            response = gemini_client.generate_content(
                sample_prompt,
                generation_config={"temperature": 0.1}
            )
            
            print(f"✅ Gemini Response:\n{response.text}")
            
        except Exception as e:
            print(f"❌ Gemini also failed: {e}")
    
    print("\n" + "=" * 50)
    print("📋 Integration Summary:")
    print(f"• Kimi K2 Available (via Groq): {'✅ Yes' if groq_client else '❌ No'}")
    print(f"• Gemini Fallback: {'✅ Yes' if gemini_client else '❌ No'}")

    if groq_client:
        print("• System will use Kimi K2 through Groq for grading")
    elif gemini_client:
        print("• System will use Gemini fallback for grading")
    else:
        print("• ⚠️ No grading client available!")

    print("\n🔧 To enable Kimi K2:")
    print("1. Ensure GROQ_API_KEY is in your .env file")
    print("2. Restart the application")
    print("3. The system will automatically use Kimi K2 through Groq when available")

if __name__ == "__main__":
    demo_grading_system()
