#!/usr/bin/env python3
"""
Database migration to add comments fields to Question and Part models.
This migration adds a 'comments' column to both 'questions' and 'parts' tables.
"""

import sqlite3
import os
import sys

def run_migration():
    """Run the migration to add comments fields"""
    
    # Get the database path
    db_path = 'instance/database.db'
    
    if not os.path.exists(db_path):
        print(f"Database file not found at {db_path}")
        print("Please make sure you're running this from the project root directory")
        return False
    
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Starting migration: Adding comments fields...")
        
        # Check if comments column already exists in questions table
        cursor.execute("PRAGMA table_info(questions)")
        questions_columns = [column[1] for column in cursor.fetchall()]
        
        if 'comments' not in questions_columns:
            print("Adding comments column to questions table...")
            cursor.execute("ALTER TABLE questions ADD COLUMN comments TEXT")
            print("✓ Added comments column to questions table")
        else:
            print("✓ Comments column already exists in questions table")
        
        # Check if comments column already exists in parts table
        cursor.execute("PRAGMA table_info(parts)")
        parts_columns = [column[1] for column in cursor.fetchall()]
        
        if 'comments' not in parts_columns:
            print("Adding comments column to parts table...")
            cursor.execute("ALTER TABLE parts ADD COLUMN comments TEXT")
            print("✓ Added comments column to parts table")
        else:
            print("✓ Comments column already exists in parts table")
        
        # Commit the changes
        conn.commit()
        print("Migration completed successfully!")
        
        return True
        
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        if conn:
            conn.close()

def rollback_migration():
    """Rollback the migration (remove comments fields)"""
    
    print("WARNING: SQLite doesn't support dropping columns directly.")
    print("To rollback this migration, you would need to:")
    print("1. Create new tables without the comments columns")
    print("2. Copy data from old tables to new tables")
    print("3. Drop old tables and rename new tables")
    print("This is a complex operation and should be done carefully.")
    print("Consider backing up your database before proceeding.")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        rollback_migration()
    else:
        success = run_migration()
        if not success:
            sys.exit(1)
