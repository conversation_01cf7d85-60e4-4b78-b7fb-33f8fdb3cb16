<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Color Highlighting Demo</title>
    <link rel="stylesheet" href="static/styles.css">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            background: #f9fafb;
            border-radius: 8px;
        }
        .demo-text {
            font-size: 16px;
            margin: 15px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }
        h1 {
            color: #1f2937;
            text-align: center;
        }
        h2 {
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 5px;
        }
        .description {
            color: #6b7280;
            font-style: italic;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>🎨 Multi-Color Highlighting System Demo</h1>
    <p style="text-align: center; color: #6b7280;">Clean, minimalistic highlighting for overlapping marking points</p>

    <div class="demo-section">
        <h2>Single Color Highlights</h2>
        <div class="description">Clean background colors, no borders or underlines</div>
        <div class="demo-text">
            The <span class="highlight-yellow highlight-base" title="Evidence found for 1 marking point">kinetic energy</span> 
            increases due to <span class="highlight-blue highlight-base" title="Evidence found for 1 marking point">acceleration</span> 
            and <span class="highlight-green highlight-base" title="Evidence found for 1 marking point">force</span> applied.
        </div>
    </div>

    <div class="demo-section">
        <h2>Two-Color Overlaps</h2>
        <div class="description">Subtle horizontal gradients showing both colors</div>
        <div class="demo-text">
            The <span class="highlight-yellow highlight-blue highlight-base" title="Evidence found for 2 marking points">kinetic energy</span> 
            increases due to <span class="highlight-green highlight-pink highlight-base" title="Evidence found for 2 marking points">acceleration and force</span> 
            applied to the <span class="highlight-purple highlight-orange highlight-base" title="Evidence found for 2 marking points">object</span>.
        </div>
    </div>

    <div class="demo-section">
        <h2>Three-Color Overlaps</h2>
        <div class="description">Distinct sections showing three overlapping marking points</div>
        <div class="demo-text">
            The <span class="highlight-multi-3 highlight-base" title="Evidence found for 3 marking points: kinetic energy | acceleration | force">kinetic energy increases</span>
            due to complex <span class="highlight-multi-3 highlight-base" title="Evidence found for 3 marking points">interactions</span>
            between multiple factors.
        </div>
    </div>

    <div class="demo-section">
        <h2>Four+ Color Overlaps</h2>
        <div class="description">Distinct color sections with subtle indicator for complex overlaps</div>
        <div class="demo-text">
            The <span class="highlight-multi-4plus highlight-base" data-count="4" title="Evidence found for 4 marking points: kinetic energy | acceleration | force | motion">fundamental physics principles</span>
            govern how <span class="highlight-multi-4plus highlight-base" data-count="5" title="Evidence found for 5 marking points">energy and motion interact</span>
            in complex systems.
        </div>
    </div>

    <div class="demo-section">
        <h2>Partial vs Full Achievement</h2>
        <div class="description">Dotted underlines in marking point colors distinguish partial achievements</div>
        <div class="demo-text">
            <strong>Fully achieved:</strong>
            <span class="highlight-yellow highlight-base" title="Evidence found for 1 marking point (fully achieved)">kinetic energy</span>
            increases due to
            <span class="highlight-blue highlight-base" title="Evidence found for 1 marking point (fully achieved)">acceleration</span>.
            <br><br>
            <strong>Partially achieved (dotted underlines):</strong>
            <span class="highlight-yellow highlight-base highlight-partial" title="Evidence found for 1 marking point (partially achieved)">kinetic energy</span>
            increases due to
            <span class="highlight-blue highlight-base highlight-partial" title="Evidence found for 1 marking point (partially achieved)">acceleration</span>.
            <br><br>
            <strong>Mixed overlaps:</strong>
            <span class="highlight-blue highlight-yellow highlight-base highlight-partial" title="Evidence found for 2 marking points (1 full, 1 partial)">energy transfer</span>
            occurs during
            <span class="highlight-green highlight-pink highlight-base" title="Evidence found for 2 marking points (both full)">collision events</span>.
            <br><br>
            <strong>Partial multi-color:</strong>
            <span class="highlight-multi-3 highlight-base highlight-partial" title="Evidence found for 3 marking points (some partial)">complex interactions</span>
            and
            <span class="highlight-multi-4plus highlight-base highlight-partial" title="Evidence found for 4+ marking points (some partial)">system dynamics</span>.
        </div>
    </div>

    <div class="demo-section">
        <h2>Mixed Example</h2>
        <div class="description">Real-world example with various overlap combinations</div>
        <div class="demo-text">
            When a <span class="highlight-yellow highlight-base" title="Evidence found for 1 marking point">ball</span>
            is thrown, its <span class="highlight-blue highlight-green highlight-base" title="Evidence found for 2 marking points">kinetic energy</span>
            depends on both <span class="highlight-multi-3 highlight-base" title="Evidence found for 3 marking points">mass and velocity</span>,
            while <span class="highlight-multi-4plus highlight-base" data-count="4" title="Evidence found for 4 marking points">gravitational forces</span>
            affect its <span class="highlight-pink highlight-base highlight-partial" title="Evidence found for 1 marking point (partially achieved)">trajectory</span>.
        </div>
    </div>

    <div class="demo-section">
        <h2>Key Features</h2>
        <ul>
            <li>✅ <strong>No annoying underlines</strong> - Clean background-only highlighting for full achievements</li>
            <li>✅ <strong>Minimalistic design</strong> - Subtle colors that don't distract from content</li>
            <li>✅ <strong>Distinct color sections</strong> - Clear separation for 3+ overlapping marking points</li>
            <li>✅ <strong>Partial achievement indicators</strong> - Dotted underlines in marking point colors</li>
            <li>✅ <strong>Hover tooltips</strong> - Detailed information on hover</li>
            <li>✅ <strong>Mobile responsive</strong> - Works well on all screen sizes</li>
            <li>✅ <strong>Accessibility friendly</strong> - High contrast and reduced motion support</li>
        </ul>
    </div>

    <script>
        // Add some interactivity to show hover effects
        document.addEventListener('DOMContentLoaded', function() {
            const highlights = document.querySelectorAll('.highlight-base');
            highlights.forEach(highlight => {
                highlight.addEventListener('mouseenter', function() {
                    console.log('Hovering over:', this.getAttribute('title'));
                });
            });
        });
    </script>
</body>
</html>
