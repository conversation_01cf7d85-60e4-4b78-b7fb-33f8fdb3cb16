from google import genai
from google.genai import types
from mistralai import Mistral

import asyncio
import os
import json
import argparse
import time
import base64
from json_repair import repair_json
from dotenv import load_dotenv
from datetime import datetime
from werkzeug.utils import secure_filename
from routes.ai_helpers import generate_marking_points

load_dotenv()

UPLOAD_FOLDER = os.getenv('UPLOAD_FOLDER', os.path.join(os.getcwd(), 'uploads'))

MISTRAL_MODEL = "mistral-ocr-latest"
GEMINI_MODEL = "gemini-2.5-flash"

COMBINED_EXTRACT_PROMPT = r"""
Task: Convert the OCR-processed markdown of a combined exam paper (containing both questions and answers) into a clean JSON output. Handle equations, multi-part questions, question types (text or MCQ), MCQ options, and metadata accurately. The output should be a JSON list of question objects WITH their answers included.

Requirements:
1. Hierarchy & Formatting
  - Flatten nested questions (e.g., Q1(a)(i) → "number": "1", "part": "a_i").
  - Retain LaTeX/equations verbatim (e.g., $F = ma$) as plaintext with \ escapes.
2. Question Type Identification
  - For each question object, determine if it is a text-based question or a Multiple Choice Question (MCQ).
  - Set the "type" field to "text" or "mcq".
3. MCQ Option Extraction
  - If the question type is "mcq", extract all its options.
  - Each option should be an object with a "text" field containing the option's content.
  - Store these options in the "options" array. For "text" type questions, "options" should be null or an empty list.
4. Answer Extraction
  - For each question, extract the corresponding answer from the document.
  - For text-based questions, the "answer" field should contain the model answer.
  - For Multiple Choice Questions (MCQs), the "answer" field must be the **exact text** of the correct option. For example, if an MCQ's correct option is "Speed", the "answer" field should be "Speed". Do not use option letters like "A", "B", "C" unless the option text itself is just a letter.
5. Metadata
  - Include score (extracted from brackets, e.g., [2] → "score": 2).
6. Attachments
  - Embed figures/diagrams using the strict naming convention used in the markdown syntax: img-<image number>
    "attachments": ["img-1", "img-2"]
  - In the output 'answer' field, do **not** include markdown image syntax (e.g., `![img-...]`).
  - If the answer text needs to refer to an image, use a general textual reference (e.g., "as shown in the diagram," "refer to the image").
7. Ensure that the output schema for each question object is followed STRICTLY.
8. At the start of some questions, there will be preambles before the first part. THE PREAMBLES ARE IMPORTANT. Insert them in our extracted data, under the first part even if it may seem that it is before the first part in original document.

Output Schema for each question object in the list:

Example of a text question object with answer:
{{
    "number": "1",
    "part": "a",
    "type": "text",
    "description": "State the two conditions necessary for a system to be in equilibrium.",
    "score": 2,
    "attachments": null,
    "options": null,
    "answer": "First, the net force acting on the system is zero. Second, the sum of clockwise moments equals sum of anticlockwise moments about any axis."
}}

Example of an MCQ question object with answer:
{{
    "number": "2",
    "part": "",
    "type": "mcq",
    "description": "Which of the following is a scalar quantity?",
    "score": 1,
    "attachments": null,
    "options": [
        {{"text": "Velocity"}},
        {{"text": "Acceleration"}},
        {{"text": "Speed"}},
        {{"text": "Force"}}
    ],
    "answer": "Speed"
}}

Example of a multi-part question, where one part is text and another is MCQ (these would be two separate objects in the output list):
// First object for 3a
{{
    "number": "3",
    "part": "a",
    "type": "text",
    "description": "Define momentum.",
    "score": 1,
    "attachments": null,
    "options": null,
    "answer": "Momentum is the product of mass and velocity of an object."
}}
// Second object for 3b
{{
    "number": "3",
    "part": "b",
    "type": "mcq",
    "description": "Which unit is used for momentum?",
    "score": 1,
    "attachments": null,
    "options": [
        {{"text": "kg m/s"}},
        {{"text": "N"}},
        {{"text": "J"}}
    ],
    "answer": "kg m/s"
}}

OCR: {ocr_content}
"""

# Keeping these for backward compatibility
EXTRACT_QUESTION_PROMPT = r"""
Task: Convert the OCR-processed markdown of the exam paper into a clean JSON output. Handle equations, multi-part questions, question types (text or MCQ), MCQ options, and metadata accurately. The output should be a JSON list of question objects.

Requirements:
1. Hierarchy & Formatting
  - Flatten nested questions (e.g., Q1(a)(i) → "number": "1", "part": "a_i").
  - Retain LaTeX/equations verbatim (e.g., $F = ma$) as plaintext with \ escapes.
2. Question Type Identification
  - For each question object, determine if it is a text-based question or a Multiple Choice Question (MCQ).
  - Set the "type" field to "text" or "mcq".
3. MCQ Option Extraction
  - If the question type is "mcq", extract all its options.
  - Each option should be an object with a "text" field containing the option's content.
  - Store these options in the "options" array. For "text" type questions, "options" should be null or an empty list.
4. Metadata
  - Include score (extracted from brackets, e.g., [2] → "score": 2).
5. Attachments
  - Embed figures/diagrams using the strict naming convention used in the markdown syntax: img-<image number>
    "attachments": ["img-1", "img-2"]
6. Ensure that the output schema for each question object is followed STRICTLY.
7. At the start of some questions, there will be preambles before the first part. THE PREAMBLES ARE IMPORTANT. Insert them in our extracted data, under the first part even if it may seem that it is before the first part in original document.
Output Schema for each question object in the list:
8. The document is structured such that the quesetions document comes entirely before the answer document. so you need to look in different areas of the document to extract the questions and answers.
9. For answers, make sure that you include ALL parts of the answer scheme, even the workings, not just the final answer. into the answer field. If you are unsure, leave blank, but do not hallucinate. 
Example of a text question object:
{{
    "number": "1",
    "part": "a",
    "type": "text",
    "description": "State the two conditions necessary for a system to be in equilibrium.",
    "score": 2,
    "attachments": null,
    "options": null
}}

Example of an MCQ question object:
{{
    "number": "2",
    "part": "",
    "type": "mcq",
    "description": "Which of the following is a scalar quantity?",
    "score": 1,
    "attachments": null,
    "options": [
        {{"text": "Velocity"}},
        {{"text": "Acceleration"}},
        {{"text": "Speed"}},
        {{"text": "Force"}}
    ]
}}

Example of a multi-part question, where one part is text and another is MCQ (these would be two separate objects in the output list):
// First object for 3a
{{
    "number": "3",
    "part": "a",
    "type": "text",
    "description": "Define momentum.",
    "score": 1,
    "attachments": null,
    "options": null
}}
// Second object for 3b
{{
    "number": "3",
    "part": "b",
    "type": "mcq",
    "description": "Which unit is used for momentum?",
    "score": 1,
    "attachments": null,
    "options": [
        {{"text": "kg m/s"}},
        {{"text": "N"}},
        {{"text": "J"}}
    ]
}}

OCR: {question_ocr}
"""

EXTRACT_ANSWERS_PROMPT = r"""
Task: Convert the OCR processed markdown of the exam paper answer scheme into a clean JSON output. Handle equations, multi-part questions, and metadata accurately. For Multiple Choice Questions (MCQs), the 'answer' field must contain the exact text of the correct option.

Requirements:
1. Hierarchy & Formatting
  - Flatten nested questions (e.g., Q1(a)(i) → "number": "1", "part": "a_i").
  - Retain LaTeX/equations verbatim (e.g., $F = ma$) as plaintext with \ escapes.
2. Answer Content:
  - For text-based questions, the "answer" field should contain the model answer.
  - For Multiple Choice Questions (MCQs), the "answer" field must be the **exact text** of the correct option. For example, if an MCQ's correct option is "Speed", the "answer" field should be "Speed". Do not use option letters like "A", "B", "C" unless the option text itself is just a letter.
3. Metadata
  - Include score (extracted from brackets, e.g., [2] → "score": 2). This is often part of the question, but if present in the answer scheme, capture it.
4. Attachments
  - The input OCR for answers may contain image references like `![img-0](img-0)`.
  - In the output 'answer' field, do **not** include markdown image syntax (e.g., `![img-...]`).
  - If the answer text needs to refer to an image, use a general textual reference (e.g., "as shown in the diagram," "refer to the image"). The actual image files are associated with the question separately.
5. Ensure that the output schema is followed STRICTLY.

Output Schema for each answer object in the list:

Example for a text question:
{{
    "number": "1",
    "part": "a",
    "answer": "First, the net force acting on the system is zero. Second, the sum of clockwise moments equals sum of anticlockwise moments about any axis."
}}

Example for an MCQ question (where the options might have been "Velocity", "Acceleration", "Speed", "Force"):
{{
    "number": "2",
    "part": "",
    "answer": "Speed"
}}

Example for a multi-part question answer with an image:
{{
    "number": "1",
    "part": "b_i",
    "answer": "Force F provides the anti-clockwise moment to counteract the existing clockwise moment due to gravity. See the provided diagram for more details."
}}

Returns:
list[answer_object]

Answer OCR: {answer_ocr}
"""

class PDFProcessor():
    def __init__(self):
        self.ocr_client = Mistral(api_key=os.getenv('MISTRAL_API_KEY'))
        self.client = genai.Client(api_key=os.getenv('GEMINI_API_KEY'))

    def get_structured_data(self, question_pdf, answer_pdf=None):
        """
        Process a PDF containing both questions and answers, or separate PDFs for questions and answers.

        Args:
            question_pdf: Path to the PDF containing questions (and possibly answers)
            answer_pdf: Optional path to a separate PDF containing answers

        Returns:
            Structured data with questions and answers
        """
        # Step 1: Call Mistral to extract raw OCR from the question PDF
        question_ocr, question_img_file = self._extract_md(question_pdf)

        # If no answer PDF is provided, assume the question PDF contains both questions and answers
        if answer_pdf is None:
            # Use the combined extraction prompt with just the question PDF
            print(f"Processing single PDF for both questions and answers")
            combined_response = self._prompt(COMBINED_EXTRACT_PROMPT.format(ocr_content=question_ocr))
            structured_data = json.loads(repair_json(combined_response.text))

            # Ensure each question has an answer field (even if empty)
            for qn in structured_data:
                if 'answer' not in qn:
                    qn['answer'] = ""

            return self._integrate_images(structured_data=structured_data, base64_qn=question_img_file)

        # For backward compatibility - if an answer PDF is provided, use the old approach with two separate calls
        print(f"Processing separate PDFs for questions and answers (legacy mode)")
        answer_ocr, ans_img_file = self._extract_md(answer_pdf)

        # Step 2: Concurrently extract structured data from answer and question pdfs
        question_response, answer_response = asyncio.run(self.process_concurrently(question_ocr, answer_ocr))

        question_response = json.loads(repair_json(question_response.text))
        answer_response = json.loads(repair_json(answer_response.text))

        # Step 3: Merge question and answer schemes and return the final structured data
        lookup_table = {}
        for ans in answer_response:
            ans_label = str(ans['number']) + str(ans.get('part', ''))
            lookup_table[ans_label] = ans['answer']

        for qn in question_response:
            qn_label = str(qn['number']) + str(qn.get('part', ''))
            qn['answer'] = lookup_table.get(qn_label, "")

        return self._integrate_images(structured_data=question_response, base64_qn=question_img_file)

    async def process_concurrently(self, question_ocr, answer_ocr):
        question_task = asyncio.to_thread(self._prompt, EXTRACT_QUESTION_PROMPT.format(question_ocr=question_ocr))
        answer_task = asyncio.to_thread(self._prompt, EXTRACT_ANSWERS_PROMPT.format(answer_ocr=answer_ocr))

        responses = await asyncio.gather(
            question_task,
            answer_task,
            return_exceptions=False
        )

        return responses[0], responses[1]

    def _extract_md(self, pdf_path):
        """
        Returns: (raw markdown content, dict mapping img id to jpeg filename)
        """
        upload = self._upload_file(pdf_path)

        print(f"Calling {MISTRAL_MODEL} for OCR processing {os.path.basename(pdf_path)}")
        response = self.ocr_client.ocr.process(
            model=MISTRAL_MODEL,
            document={
                "type": "document_url",
                "document_url": self.ocr_client.files.get_signed_url(file_id=upload.id).url
            },
            include_image_base64=True
        )
        print(f"OCR processing of {os.path.basename(pdf_path)} successful")

        total_md = ""
        img_id = {}
        for page in response.pages:
            total_md += page.markdown
            if hasattr(page, 'images') and isinstance(page.images, list):
                for image in page.images:
                    filename= self._save_base64_as_jpeg(image.image_base64, image.id)
                    img_id[image.id] = filename
        #with open('response.txt',"w") as f:
            #f.write(total_md)
        return (total_md, img_id)

    def _upload_file(self, pdf_path):
        print(f"Uploading {os.path.basename(pdf_path)}...")

        try:
            with open(pdf_path, "rb") as file:
                upload = self.ocr_client.files.upload(
                    file={
                        "file_name": os.path.basename(file.name),
                        "content": file,
                    },
                    purpose="ocr"
                )
                print(f"Successfully uploaded {os.path.basename(file.name)}")

            return upload
        except Exception as e:
            raise ValueError(f"Failed to upload file {e}")

    def _prompt(self, prompt):
        print(f"Calling {GEMINI_MODEL} for extracting structured data")
        response = self.client.models.generate_content(
            model=GEMINI_MODEL,
            contents=prompt,
            config=types.GenerateContentConfig(
              temperature=0.1,
              response_mime_type="application/json"
            ),
        )
        #with open("fixed_response.txt","w") as f:
            #f.write(response.text)
        print(f"{GEMINI_MODEL} finished extracting structured data")
        return response

    def _integrate_images(self, structured_data, base64_qn, base64_ans=None):
        for question in structured_data:
            if 'attachments' not in question or question['attachments'] is None:
                continue

            b64_list = []
            for img in question['attachments']:
                img_name = f"{img}.jpeg"
                if base64_qn.get(img_name) != None:
                    b64_list.append(base64_qn[img_name])

            question['attachments'] = b64_list

        return structured_data

    def _save_base64_as_jpeg(self, base64_string: str, image_id: str):
        """Saves a base64 encoded image string to a file in UPLOAD_FOLDER with a unique name."""
        try:
            # Generate a secure base filename from the image ID
            base_filename = secure_filename(image_id)
            # Create a unique filename by appending a timestamp
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S%f")
            name, ext = os.path.splitext(base_filename)
            # Ensure there's an extension, default to .jpeg if none
            if not ext:
                ext = '.jpeg'
            final_filename = f"{name}_{timestamp}{ext}"
            save_path = os.path.join(UPLOAD_FOLDER, final_filename)

            # Remove data URI prefix if present (handle potential variations)
            if ';base64,' in base64_string:
                base64_string = base64_string.split(',')[1]

            # Decode base64 and write to file
            image_data = base64.b64decode(base64_string)
            with open(save_path, 'wb') as f:
                f.write(image_data)
            print(f"Successfully saved image: {save_path}")
            return final_filename
        except Exception as e:
            print(f"Error saving image with id {image_id} to {final_filename}: {e}")
            return None

if __name__ == '__main__':
    parser = argparse.ArgumentParser("Test full pdf extraction pipeline")
    parser.add_argument("question_pdf", help="Path to the question pdf")
    parser.add_argument("answer_pdf", help="Path to the answer pdf", nargs='?', default=None)
    args = parser.parse_args()

    START_TIME = time.time()
    worker = PDFProcessor()
    print(worker.get_structured_data(question_pdf=args.question_pdf, answer_pdf=args.answer_pdf))
    END_TIME = time.time()
    print(f"Execution took {END_TIME - START_TIME} seconds")
