from flask import render_template, request, jsonify, session, flash, redirect, url_for
from sqlalchemy import func, desc, asc
from models import db, User, Subject, Topic, Question, Part, Submission, Group, user_group_association
from .utils import admin_required, error_logger, app_logger
import json


def register_teacher_routes(app, db, session):
    """Register teacher dashboard routes"""

    @app.route("/teacher")
    @admin_required
    def teacher_dashboard():
        """Teacher dashboard with submission analytics"""
        try:
            # Get all subjects for filtering
            subjects = Subject.query.order_by(Subject.name).all()

            # Get all groups for filtering (teachers can see all groups)
            user_id = session.get('user_id')
            groups = Group.query.order_by(Group.name).all()

            # Get initial data (all submissions)
            analytics_data = get_submission_analytics()

            return render_template('teacher_dashboard.html',
                                 title="Teacher Dashboard",
                                 subjects=subjects,
                                 groups=groups,
                                 analytics_data=analytics_data)
        except Exception as e:
            error_logger.exception("Error loading teacher dashboard")
            flash("Error loading teacher dashboard.", "error")
            return redirect(url_for('index'))

    @app.route("/api/teacher/analytics")
    @admin_required
    def get_teacher_analytics():
        """API endpoint for filtered and sorted analytics data"""
        try:
            subject_id = request.args.get('subject_id', type=int)
            topic_id = request.args.get('topic_id', type=int)
            group_id = request.args.get('group_id', type=int)
            sort_by = request.args.get('sort_by', 'avg_score_asc')
            view_mode = request.args.get('view_mode', 'users')  # 'users' or 'questions'

            if view_mode == 'questions':
                # Return question-level analytics when in questions mode
                question_data = get_question_analytics(subject_id, topic_id, group_id, sort_by)
                return jsonify({
                    'status': 'success',
                    'data': {
                        'view_mode': 'questions',
                        'question_analytics': question_data,
                        'summary': {
                            'total_questions': len(question_data),
                            'avg_difficulty': calculate_avg_difficulty(question_data),
                            'total_attempts': sum(q['total_attempts'] for q in question_data)
                        }
                    }
                })
            else:
                # Return user-level analytics (default behavior)
                analytics_data = get_submission_analytics(
                    subject_id=subject_id,
                    topic_id=topic_id,
                    group_id=group_id,
                    sort_by=sort_by
                )
                analytics_data['view_mode'] = 'users'
                return jsonify({
                    'status': 'success',
                    'data': analytics_data
                })

        except Exception as e:
            error_logger.exception("Error getting teacher analytics")
            return jsonify({
                'status': 'error',
                'message': 'Failed to load analytics data'
            }), 500

    @app.route("/api/teacher/topics/<int:subject_id>")
    @admin_required
    def get_topics_for_subject(subject_id):
        """Get topics for a specific subject"""
        try:
            topics = Topic.query.filter_by(subject_id=subject_id).order_by(Topic.name).all()
            topics_data = [{'id': topic.id, 'name': topic.name} for topic in topics]
            
            return jsonify({
                'status': 'success',
                'topics': topics_data
            })
        except Exception as e:
            error_logger.exception(f"Error getting topics for subject {subject_id}")
            return jsonify({
                'status': 'error',
                'message': 'Failed to load topics'
            }), 500

    @app.route("/api/teacher/question-analytics")
    @admin_required
    def get_question_analytics_api():
        """API endpoint for question-level analytics"""
        try:
            subject_id = request.args.get('subject_id', type=int)
            topic_id = request.args.get('topic_id', type=int)

            question_data = get_question_analytics(subject_id, topic_id)

            return jsonify({
                'status': 'success',
                'data': question_data
            })

        except Exception as e:
            error_logger.exception("Error loading question analytics")
            return jsonify({
                'status': 'error',
                'message': 'Failed to load question analytics'
            }), 500

    @app.route("/api/teacher/groups")
    @admin_required
    def get_teacher_groups():
        """Get groups owned by the current teacher"""
        try:
            user_id = session.get('user_id')
            if not user_id:
                return jsonify({'status': 'error', 'message': 'Not authenticated'}), 401

            # Get groups owned by this teacher
            owned_groups = Group.query.filter_by(owner_id=user_id).order_by(Group.name).all()

            groups_data = []
            for group in owned_groups:
                groups_data.append({
                    'id': group.id,
                    'name': group.name,
                    'description': group.description,
                    'invite_code': group.invite_code,
                    'member_count': len(group.members)
                })

            return jsonify({
                'status': 'success',
                'groups': groups_data
            })
        except Exception as e:
            error_logger.exception("Error getting teacher groups")
            return jsonify({
                'status': 'error',
                'message': 'Failed to load groups'
            }), 500

    @app.route("/api/teacher/create-default-group", methods=['POST'])
    @admin_required
    def create_default_group():
        """Create a default group for the teacher and add all existing users to it"""
        try:
            user_id = session.get('user_id')
            if not user_id:
                return jsonify({'status': 'error', 'message': 'Not authenticated'}), 401

            current_user = User.query.get(user_id)
            if not current_user:
                return jsonify({'status': 'error', 'message': 'User not found'}), 404

            # Check if teacher already has groups
            existing_groups = Group.query.filter_by(owner_id=user_id).count()
            if existing_groups > 0:
                return jsonify({'status': 'error', 'message': 'You already have groups created'}), 400

            # Create default group
            group_name = f"{current_user.username}'s Class"
            new_group = Group(
                name=group_name,
                description="Default class group for all students",
                owner_id=user_id
            )

            # Generate invite code
            import random
            import string
            new_group.invite_code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))

            # Add the teacher as a member
            new_group.members.append(current_user)

            # Add all existing non-admin users to the group
            all_students = User.query.filter(User.role != 'admin', User.id != user_id).all()
            for student in all_students:
                new_group.members.append(student)

            db.session.add(new_group)
            db.session.commit()

            app_logger.info(f"Created default group '{group_name}' for teacher {current_user.username} with {len(all_students)} students")

            return jsonify({
                'status': 'success',
                'message': f'Created default group "{group_name}" with {len(all_students)} students',
                'group': {
                    'id': new_group.id,
                    'name': new_group.name,
                    'description': new_group.description,
                    'invite_code': new_group.invite_code,
                    'member_count': len(new_group.members)
                }
            })

        except Exception as e:
            db.session.rollback()
            error_logger.exception("Error creating default group")
            return jsonify({
                'status': 'error',
                'message': 'Failed to create default group'
            }), 500

    @app.route("/api/teacher/groups/<int:group_id>/generate-invite", methods=['POST'])
    @admin_required
    def generate_group_invite(group_id):
        """Generate a new invite code for a group"""
        try:
            user_id = session.get('user_id')
            if not user_id:
                return jsonify({'status': 'error', 'message': 'Not authenticated'}), 401

            # Check if the user owns this group
            group = Group.query.filter_by(id=group_id, owner_id=user_id).first()
            if not group:
                return jsonify({'status': 'error', 'message': 'Group not found or access denied'}), 404

            # Generate new invite code
            invite_code = group.generate_invite_code()
            db.session.commit()

            app_logger.info(f"User {user_id} generated invite code {invite_code} for group {group.name}")

            return jsonify({
                'status': 'success',
                'invite_code': invite_code,
                'message': f'Invite code generated for group "{group.name}"'
            })
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error generating invite code for group {group_id}")
            return jsonify({
                'status': 'error',
                'message': 'Failed to generate invite code'
            }), 500

    @app.route("/api/teacher/groups/<int:group_id>/revoke-invite", methods=['POST'])
    @admin_required
    def revoke_group_invite(group_id):
        """Revoke the invite code for a group"""
        try:
            user_id = session.get('user_id')
            if not user_id:
                return jsonify({'status': 'error', 'message': 'Not authenticated'}), 401

            # Check if the user owns this group
            group = Group.query.filter_by(id=group_id, owner_id=user_id).first()
            if not group:
                return jsonify({'status': 'error', 'message': 'Group not found or access denied'}), 404

            # Revoke invite code
            group.revoke_invite_code()
            db.session.commit()

            app_logger.info(f"User {user_id} revoked invite code for group {group.name}")

            return jsonify({
                'status': 'success',
                'message': f'Invite code revoked for group "{group.name}"'
            })
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error revoking invite code for group {group_id}")
            return jsonify({
                'status': 'error',
                'message': 'Failed to revoke invite code'
            }), 500


def get_submission_analytics(subject_id=None, topic_id=None, group_id=None, sort_by='avg_score_asc'):
    """
    Get submission analytics data with optional filtering and sorting
    Only shows data for groups owned by the current teacher

    Args:
        subject_id: Filter by specific subject
        topic_id: Filter by specific topic
        group_id: Filter by specific group (must be owned by current teacher)
        sort_by: Sort criteria ('avg_score_asc', 'avg_score_desc', 'submissions_asc', 'submissions_desc')

    Returns:
        Dictionary containing analytics data
    """
    try:
        # Get current teacher's user ID
        from flask import session
        teacher_id = session.get('user_id')
        if not teacher_id:
            return {
                'user_analytics': [],
                'summary': {
                    'total_users': 0,
                    'total_questions_attempted': 0,
                    'total_points_scored': 0,
                    'avg_questions_per_user': 0,
                    'avg_points_per_user': 0
                }
            }

        # Get current user info
        current_user = User.query.get(teacher_id)
        if not current_user:
            return {
                'user_analytics': [],
                'summary': {
                    'total_users': 0,
                    'total_questions_attempted': 0,
                    'total_points_scored': 0,
                    'avg_questions_per_user': 0,
                    'avg_points_per_user': 0
                }
            }

        # For admin/teacher users, show all students (non-admin users)
        # Get all users with basic info, excluding other admins
        users_query = db.session.query(
            User.id,
            User.username,
            User.email,
            User.grade_level
        ).filter(User.role != 'admin')  # Exclude other admins/teachers

        # If a specific group is requested, filter by that group
        if group_id:
            # Get the specific group and its members
            group = Group.query.get(group_id)
            if not group:
                return {
                    'user_analytics': [],
                    'summary': {
                        'total_users': 0,
                        'total_questions_attempted': 0,
                        'total_points_scored': 0,
                        'avg_questions_per_user': 0,
                        'avg_points_per_user': 0
                    }
                }

            # Filter users to only those in the specified group
            users_query = users_query.join(user_group_association, User.id == user_group_association.c.user_id).filter(
                user_group_association.c.group_id == group_id
            )

        users = users_query.distinct().all()

        # Calculate statistics for each user
        user_analytics = []
        for user in users:
            # Build base query for this user's submissions
            submission_query = db.session.query(Submission).filter(
                Submission.user_id == user.id
            ).join(
                Part, Submission.part_id == Part.id
            ).join(
                Question, Part.question_id == Question.id
            ).join(
                Topic, Question.topic_id == Topic.id
            ).join(
                Subject, Topic.subject_id == Subject.id
            )

            # Apply subject/topic filters
            if subject_id:
                submission_query = submission_query.filter(Subject.id == subject_id)
            if topic_id:
                submission_query = submission_query.filter(Topic.id == topic_id)

            submissions = submission_query.all()

            if not submissions:
                # User has no submissions matching the filters
                user_analytics.append({
                    'user_id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'grade_level': user.grade_level or 'Not specified',
                    'questions_attempted': 0,
                    'total_points': 0,
                    'last_submission_date': 'Never'
                })
                continue

            # Calculate questions attempted (distinct questions)
            questions_attempted = len(set(s.question_id for s in submissions))

            # Calculate total points (sum of max scores per question)
            # Group submissions by question_id and part_id, then get max score for each part
            question_part_scores = {}
            for submission in submissions:
                key = (submission.question_id, submission.part_id)
                if key not in question_part_scores:
                    question_part_scores[key] = submission.score or 0
                else:
                    question_part_scores[key] = max(question_part_scores[key], submission.score or 0)

            # Sum max scores per question
            question_totals = {}
            for (question_id, part_id), max_score in question_part_scores.items():
                if question_id not in question_totals:
                    question_totals[question_id] = 0
                question_totals[question_id] += max_score

            total_points = sum(question_totals.values())

            # Get last submission date
            last_submission_date = max(s.timestamp for s in submissions).strftime('%Y-%m-%d %H:%M')

            user_analytics.append({
                'user_id': user.id,
                'username': user.username,
                'email': user.email,
                'grade_level': user.grade_level or 'Not specified',
                'questions_attempted': questions_attempted,
                'total_points': round(total_points, 2),
                'last_submission_date': last_submission_date
            })

        # Apply sorting
        if sort_by == 'avg_score_asc':
            # Sort by total_points ascending (since we no longer have avg_score)
            user_analytics.sort(key=lambda x: x['total_points'])
        elif sort_by == 'avg_score_desc':
            # Sort by total_points descending
            user_analytics.sort(key=lambda x: x['total_points'], reverse=True)
        elif sort_by == 'submissions_asc':
            # Sort by questions_attempted ascending
            user_analytics.sort(key=lambda x: x['questions_attempted'])
        elif sort_by == 'submissions_desc':
            # Sort by questions_attempted descending
            user_analytics.sort(key=lambda x: x['questions_attempted'], reverse=True)

        # Get overall statistics
        total_users = len(user_analytics)
        total_questions_attempted = sum(user['questions_attempted'] for user in user_analytics)
        total_points_scored = sum(user['total_points'] for user in user_analytics)
        avg_questions_per_user = total_questions_attempted / total_users if total_users > 0 else 0
        avg_points_per_user = total_points_scored / total_users if total_users > 0 else 0

        return {
            'user_analytics': user_analytics,
            'summary': {
                'total_users': total_users,
                'total_questions_attempted': total_questions_attempted,
                'total_points_scored': round(total_points_scored, 2),
                'avg_questions_per_user': round(avg_questions_per_user, 2),
                'avg_points_per_user': round(avg_points_per_user, 2)
            }
        }

    except Exception as e:
        error_logger.exception("Error in get_submission_analytics")
        return {
            'user_analytics': [],
            'summary': {
                'total_users': 0,
                'total_submissions': 0,
                'overall_avg_score': 0,
                'performance_distribution': {
                    'excellent': 0,
                    'good': 0,
                    'needs_improvement': 0
                }
            }
        }


def calculate_avg_difficulty(question_data):
    """Calculate average difficulty based on average scores"""
    if not question_data:
        return 0
    total_avg = sum(q['avg_score'] for q in question_data)
    return round(total_avg / len(question_data), 2)


def get_question_analytics(subject_id=None, topic_id=None, group_id=None, sort_by='avg_score_asc'):
    """
    Get question-level analytics showing which questions are most/least attempted
    and their average scores

    Args:
        subject_id: Filter by specific subject
        topic_id: Filter by specific topic
        group_id: Filter by specific group
        sort_by: Sort criteria for questions

    Returns:
        List of question analytics
    """
    try:
        # Query for question-level statistics
        # We need to aggregate scores at the question level, not part level
        # First, get all submissions with their question info
        subquery = db.session.query(
            Question.id.label('question_id'),
            Question.title,
            Question.source,
            Topic.name.label('topic_name'),
            Subject.name.label('subject_name'),
            Submission.user_id,
            Submission.score,
            Submission.id.label('submission_id')
        ).join(
            Topic, Question.topic_id == Topic.id
        ).join(
            Subject, Topic.subject_id == Subject.id
        ).join(
            Part, Question.id == Part.question_id
        ).join(
            Submission, Part.id == Submission.part_id
        ).join(
            User, Submission.user_id == User.id
        )

        # Apply filters to subquery
        if subject_id:
            subquery = subquery.filter(Subject.id == subject_id)
        if topic_id:
            subquery = subquery.filter(Topic.id == topic_id)
        if group_id:
            # Filter submissions by users in the specified group
            subquery = subquery.join(user_group_association, User.id == user_group_association.c.user_id).filter(
                user_group_association.c.group_id == group_id
            )

        subquery = subquery.subquery()

        # Now aggregate by question
        query = db.session.query(
            subquery.c.question_id.label('id'),
            subquery.c.title,
            subquery.c.source,
            subquery.c.topic_name,
            subquery.c.subject_name,
            func.count(subquery.c.submission_id).label('total_attempts'),
            func.count(func.distinct(subquery.c.user_id)).label('unique_students'),
            func.avg(subquery.c.score).label('avg_score'),
            func.min(subquery.c.score).label('min_score'),
            func.max(subquery.c.score).label('max_score')
        ).group_by(
            subquery.c.question_id,
            subquery.c.title,
            subquery.c.source,
            subquery.c.topic_name,
            subquery.c.subject_name
        )



        # Apply sorting
        if sort_by == 'avg_score_asc':
            query = query.order_by(asc('avg_score'))
        elif sort_by == 'avg_score_desc':
            query = query.order_by(desc('avg_score'))
        elif sort_by == 'submissions_asc':
            query = query.order_by(asc('total_attempts'))
        elif sort_by == 'submissions_desc':
            query = query.order_by(desc('total_attempts'))
        else:
            # Default: order by total attempts descending
            query = query.order_by(desc('total_attempts'))

        results = query.all()

        question_analytics = []
        for result in results:
            avg_score = float(result.avg_score or 0) * 100  # Convert to percentage
            difficulty_level = 'Easy' if avg_score >= 80 else 'Medium' if avg_score >= 60 else 'Hard'

            question_analytics.append({
                'question_id': result.id,
                'title': result.title or f"Question {result.id}",
                'source': result.source or 'Unknown',
                'topic_name': result.topic_name,
                'subject_name': result.subject_name,
                'total_attempts': result.total_attempts,
                'unique_students': result.unique_students,
                'avg_score': round(avg_score, 2),
                'min_score': float(result.min_score or 0) * 100,  # Convert to percentage
                'max_score': float(result.max_score or 0) * 100,  # Convert to percentage
                'difficulty_level': difficulty_level
            })

        return question_analytics

    except Exception as e:
        error_logger.exception("Error in get_question_analytics")
        return []
