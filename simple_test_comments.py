#!/usr/bin/env python3
"""
Simple test script to verify that the comments functionality is working correctly.
This script tests the database schema and code integration without importing the full app.
"""

import sqlite3
import os

def test_database_schema():
    """Test that the comments columns exist in the database"""
    print("Testing database schema...")
    
    db_path = 'instance/database.db'
    if not os.path.exists(db_path):
        print("❌ Database file not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check questions table
        cursor.execute("PRAGMA table_info(questions)")
        questions_columns = [column[1] for column in cursor.fetchall()]
        
        if 'comments' not in questions_columns:
            print("❌ Comments column missing from questions table")
            return False
        else:
            print("✅ Comments column found in questions table")
        
        # Check parts table
        cursor.execute("PRAGMA table_info(parts)")
        parts_columns = [column[1] for column in cursor.fetchall()]
        
        if 'comments' not in parts_columns:
            print("❌ Comments column missing from parts table")
            return False
        else:
            print("✅ Comments column found in parts table")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_models_py():
    """Test that models.py has been updated with comments fields"""
    print("\nTesting models.py...")
    
    try:
        with open('models.py', 'r') as f:
            models_content = f.read()
        
        # Check Question model
        if 'comments = db.Column(db.Text, nullable=True)  # Additional context for grading' in models_content:
            print("✅ Question model has comments field")
        else:
            print("❌ Question model missing comments field")
            return False
        
        # Check Part model  
        if 'comments = db.Column(db.Text, nullable=True)  # Additional context for grading' in models_content:
            print("✅ Part model has comments field")
        else:
            print("❌ Part model missing comments field")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading models.py: {e}")
        return False

def test_grading_prompt_integration():
    """Test that comments are included in grading prompts"""
    print("\nTesting grading prompt integration...")
    
    try:
        with open('routes/api.py', 'r') as f:
            api_content = f.read()
        
        # Check if the comments integration code exists
        if 'part_data.question.comments' in api_content and 'part_data.comments' in api_content:
            print("✅ Comments integration found in grading prompts")
        else:
            print("❌ Comments integration not found in grading prompts")
            return False
        
        # Check for the additional context building
        if 'additional_context = ""' in api_content and 'QUESTION CONTEXT:' in api_content:
            print("✅ Additional context building found")
        else:
            print("❌ Additional context building not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking grading prompt integration: {e}")
        return False

def test_template_updates():
    """Test that the edit question template has been updated"""
    print("\nTesting template updates...")
    
    try:
        with open('templates/edit_question.html', 'r') as f:
            template_content = f.read()
        
        # Check for question comments field
        if 'Comments for Grading' in template_content and 'question.comments' in template_content:
            print("✅ Question comments field found in template")
        else:
            print("❌ Question comments field not found in template")
            return False
        
        # Check for part comments field
        if 'part.comments' in template_content:
            print("✅ Part comments field found in template")
        else:
            print("❌ Part comments field not found in template")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking template: {e}")
        return False

def test_admin_routes_updates():
    """Test that admin routes have been updated to handle comments"""
    print("\nTesting admin routes updates...")
    
    try:
        with open('routes/admin.py', 'r') as f:
            admin_content = f.read()
        
        # Check for question comments handling
        if "question.comments = request.form.get('comments')" in admin_content:
            print("✅ Question comments handling found in edit_question")
        else:
            print("❌ Question comments handling not found in edit_question")
            return False
        
        # Check for part comments handling
        if "part.comments = request.form.get(f'part_{part.id}_comments', '')" in admin_content:
            print("✅ Part comments handling found in edit_question")
        else:
            print("❌ Part comments handling not found in edit_question")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking admin routes: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Comments Functionality")
    print("=" * 50)
    
    tests = [
        test_database_schema,
        test_models_py,
        test_grading_prompt_integration,
        test_template_updates,
        test_admin_routes_updates
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Comments functionality is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
