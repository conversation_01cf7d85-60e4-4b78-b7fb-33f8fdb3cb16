{% extends "base.html" %}

{% block content %}
<!-- Hero Section -->
<div class="relative isolate overflow-hidden bg-gradient-to-b from-indigo-50/20">
    <div class="absolute inset-0 -z-10 h-full w-full bg-white">
        <div class="absolute inset-0 bg-[linear-gradient(to_right,#f0f0f0_1px,transparent_1px),linear-gradient(to_bottom,#f0f0f0_1px,transparent_1px)] bg-[size:6rem_4rem] motion-safe:animate-[grid_20s_linear_infinite]"></div>
        <div class="absolute inset-0 flex items-center justify-center opacity-5">
            <div class="grid grid-cols-4 gap-8">
                <i class="fas fa-arrow-up text-4xl text-indigo-700 animate-float"></i>
                <i class="fas fa-trophy text-4xl text-violet-700 animate-float-delayed"></i>
                <i class="fas fa-star text-4xl text-indigo-700 animate-float"></i>
                <i class="fas fa-medal text-4xl text-violet-700 animate-float-delayed"></i>
                <i class="fas fa-chart-line text-4xl text-indigo-700 animate-float"></i>
                <i class="fas fa-graduation-cap text-4xl text-violet-700 animate-float-delayed"></i>
                <i class="fas fa-lightbulb text-4xl text-indigo-700 animate-float"></i>
                <i class="fas fa-award text-4xl text-violet-700 animate-float-delayed"></i>
            </div>
        </div>
    </div>
    <div class="mx-auto max-w-7xl px-6 pb-24 pt-10 sm:pb-32 lg:flex lg:px-8 lg:py-40">
        <div class="mx-auto max-w-2xl flex-shrink-0 lg:mx-0 lg:max-w-xl lg:pt-8 opacity-0 translate-y-4 transition-all duration-1000" id="hero-content">
            <div class="mt-24 sm:mt-32 lg:mt-16">
                <!-- Badge -->
                <div class="flex items-center space-x-6 mb-8">
                    <div class="relative w-24 h-24 transform hover:scale-105 transition-all duration-300">
                        <img src="{{ url_for('static', filename='images/ricrest.jpg') }}" alt="Raffles Institution Crest" class="w-full h-full object-contain">
                        <div class="absolute inset-0 ring-1 ring-black/10 rounded-lg"></div>
                    </div>
                    <div class="flex flex-col">
                        <span class="text-sm font-semibold text-indigo-700">Your Journey to</span>
                        <span class="text-2xl font-bold text-gray-900">Excellence</span>
                        <span class="text-sm italic text-violet-600 mt-1">Starts with Vast</span>
                    </div>
                </div>
                <a href="#how-it-works" class="inline-flex space-x-6 group">
                    <span class="rounded-full bg-indigo-500/10 px-3 py-1 text-sm font-semibold leading-6 text-indigo-700 ring-1 ring-inset ring-indigo-500/20 group-hover:bg-indigo-500/20 transition-colors duration-200">How It Works</span>
                    <span class="inline-flex items-center space-x-2 text-sm font-medium leading-6 text-gray-600 group-hover:text-indigo-700 transition-colors duration-200">
                        <span>See the difference</span>
                        <svg class="h-5 w-5 text-gray-400 group-hover:text-indigo-700 group-hover:translate-x-1 transition-all duration-200" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                        </svg>
                    </span>
                </a>
            </div>
            <h1 class="mt-10 text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl bg-gradient-to-r from-indigo-700 via-violet-700 to-purple-700 bg-clip-text text-transparent hover:glow transition-all duration-300">From Struggling Student to Top of Your Class</h1>
            <p class="mt-6 text-lg leading-8 text-gray-600">Your academic breakthrough is waiting. Join thousands of students who transformed their grades and confidence with our proven learning system.</p>
            <div class="mt-10 flex items-center gap-x-6">
                {% if not session.get('user_id') %}
                <a href="{{ url_for('register') }}" class="rounded-md bg-indigo-700 px-5 py-3 text-sm font-semibold text-white shadow-sm hover:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-700 transform hover:scale-105 transition-all duration-200">Begin Your Transformation</a>
                <a href="{{ url_for('login') }}" class="text-sm font-semibold leading-6 text-gray-900 hover:text-indigo-700 transition-colors duration-200 group">Continue Your Journey <span aria-hidden="true" class="group-hover:translate-x-1 inline-block transition-transform duration-200">→</span></a>
                {% else %}
                <button id="start-vault-tour-button" class="rounded-md bg-indigo-700 px-5 py-3 text-sm font-semibold text-white shadow-sm hover:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-700 transform hover:scale-105 transition-all duration-200">
                    <i class="fas fa-play-circle mr-2"></i>Start/Restart Tour
                </button>
                {% endif %}
            </div>
        </div>
        <div class="mx-auto mt-16 flex max-w-2xl sm:mt-24 lg:ml-10 lg:mr-0 lg:mt-0 lg:max-w-none lg:flex-none xl:ml-32 opacity-0 translate-x-4 transition-all duration-1000" id="hero-image">
            <div class="max-w-3xl flex-none sm:max-w-5xl lg:max-w-none">
                <div class="rounded-xl bg-gradient-to-br from-indigo-50 via-white to-violet-50 p-8 shadow-xl ring-1 ring-inset ring-gray-200 transform hover:scale-105 transition-all duration-300">
                    <div class="flex flex-col h-full items-center justify-center space-y-4">
                        <div class="relative">
                            <i class="fas fa-user-graduate fa-6x text-transparent bg-gradient-to-r from-indigo-700 via-violet-700 to-purple-700 bg-clip-text animate-float"></i>
                            <div class="absolute -top-2 -right-2 bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-xs font-bold animate-pulse">A</div>
                        </div>
                        <div class="text-center">
                            <div class="flex items-center justify-center space-x-2">
                                <div class="h-2 w-16 bg-red-400 rounded-full"></div>
                                <i class="fas fa-arrow-right text-gray-400"></i>
                                <div class="h-2 w-16 bg-yellow-400 rounded-full"></div>
                                <i class="fas fa-arrow-right text-gray-400"></i>
                                <div class="h-2 w-16 bg-green-400 rounded-full"></div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">Your grade improvement journey</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>





<!-- How VAST Works Section - Modern Timeline Design -->
<div class="bg-gradient-to-b from-gray-50 to-white py-24 sm:py-32" id="how-it-works">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-4xl lg:text-center opacity-0 translate-y-4 transition-all duration-1000" id="how-it-works-header">
            <h2 class="text-base font-semibold leading-7 text-indigo-600">The VAST Difference</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl bg-gradient-to-r from-indigo-700 via-violet-700 to-purple-700 bg-clip-text text-transparent">Learning Reimagined</p>
            <p class="mt-6 text-lg leading-8 text-gray-600">See how VAST transforms the traditional learning cycle, turning <span class="line-through text-red-400">weeks</span> into <span class="font-semibold text-green-500">seconds</span> of progress.</p>
        </div>

        <div class="mt-16 sm:mt-20 lg:mt-24 opacity-0 translate-y-4 transition-all duration-1000" id="how-it-works-content">
            <!-- Timeline Comparison Container -->
            <div class="relative grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-stretch">
                <!-- Connecting Line (visible only on desktop) -->
                <div class="absolute left-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-red-300 via-purple-400 to-green-400 hidden lg:block transform -translate-x-1/2 z-0"></div>
                
                <!-- Traditional Method Card -->
                <div class="relative bg-white rounded-2xl shadow-lg overflow-hidden transform hover:translate-y-[-4px] transition-all duration-300 z-10">
                    <!-- Top Gradient Bar -->
                    <div class="h-2 bg-gradient-to-r from-red-400 to-red-600"></div>
                    
                    <div class="p-6 sm:p-8">
                        <!-- Header -->
                        <div class="flex items-center mb-8">
                            <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mr-4 shadow-sm">
                                <i class="fas fa-hourglass-half text-red-500 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-xl sm:text-2xl font-bold text-gray-800">Traditional Learning</h3>
                                <p class="text-sm text-red-500 font-medium">The Old Way</p>
                            </div>
                        </div>
                        
                        <!-- Timeline Items -->
                        <div class="space-y-6">
                            <!-- Item 1 -->
                            <div class="flex group">
                                <div class="mr-4 flex-shrink-0">
                                    <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center shadow-sm group-hover:bg-red-200 transition-colors duration-300">
                                        <i class="fas fa-pencil-alt text-red-500"></i>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-gray-800 group-hover:text-red-500 transition-colors duration-300">Complete Assignment</h4>
                                    <p class="text-sm text-gray-600 mt-1">Work with limited guidance and uncertainty</p>
                                    <div class="flex items-center mt-2">
                                        <i class="far fa-clock text-red-400 mr-1"></i>
                                        <span class="text-xs font-medium text-red-500">Hours to Days</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Item 2 -->
                            <div class="flex group">
                                <div class="mr-4 flex-shrink-0">
                                    <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center shadow-sm group-hover:bg-red-200 transition-colors duration-300">
                                        <i class="fas fa-calendar-alt text-red-500"></i>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-gray-800 group-hover:text-red-500 transition-colors duration-300">Wait for Submission</h4>
                                    <p class="text-sm text-gray-600 mt-1">Wait for deadline or next class to submit</p>
                                    <div class="flex items-center mt-2">
                                        <i class="far fa-clock text-red-400 mr-1"></i>
                                        <span class="text-xs font-medium text-red-500">Days to Weeks</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Item 3 -->
                            <div class="flex group">
                                <div class="mr-4 flex-shrink-0">
                                    <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center shadow-sm group-hover:bg-red-200 transition-colors duration-300">
                                        <i class="fas fa-user-clock text-red-500"></i>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-gray-800 group-hover:text-red-500 transition-colors duration-300">Teacher Marks Work</h4>
                                    <p class="text-sm text-gray-600 mt-1">Teacher processes large batches of assignments</p>
                                    <div class="flex items-center mt-2">
                                        <i class="far fa-clock text-red-400 mr-1"></i>
                                        <span class="text-xs font-medium text-red-500">Days to Weeks</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Item 4 -->
                            <div class="flex group">
                                <div class="mr-4 flex-shrink-0">
                                    <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center shadow-sm group-hover:bg-red-200 transition-colors duration-300">
                                        <i class="fas fa-redo-alt text-red-500"></i>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-gray-800 group-hover:text-red-500 transition-colors duration-300">Receive & Apply Feedback</h4>
                                    <p class="text-sm text-gray-600 mt-1">Get delayed feedback and try to correct mistakes</p>
                                    <div class="flex items-center mt-2">
                                        <i class="far fa-clock text-red-400 mr-1"></i>
                                        <span class="text-xs font-medium text-red-500">Days to Weeks</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Total Time -->
                        <div class="mt-8 pt-6 border-t border-gray-100">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-500">Total Learning Cycle:</span>
                                <span class="text-lg font-bold text-red-500">Weeks to Months</span>
                            </div>
                            <div class="w-full h-2 bg-gray-100 rounded-full mt-2 overflow-hidden">
                                <div class="h-full bg-red-400 rounded-full animate-pulse" style="width: 100%"></div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2 text-right">Slow progress, lost momentum</p>
                        </div>
                    </div>
                </div>
                
                <!-- VAST Method Card -->
                <div class="relative bg-gradient-to-br from-indigo-50 to-violet-50 rounded-2xl shadow-lg overflow-hidden transform hover:translate-y-[-4px] transition-all duration-300 z-10">
                    <!-- Top Gradient Bar -->
                    <div class="h-2 bg-gradient-to-r from-indigo-500 to-violet-600"></div>
                    
                    <div class="p-6 sm:p-8">
                        <!-- Header -->
                        <div class="flex items-center mb-8">
                            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-indigo-500 to-violet-600 flex items-center justify-center mr-4 shadow-md">
                                <i class="fas fa-bolt text-white text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-xl sm:text-2xl font-bold text-gray-800">VAST Learning</h3>
                                <p class="text-sm text-indigo-600 font-medium">The Future of Education</p>
                            </div>
                        </div>
                        
                        <!-- Timeline Items -->
                        <div class="space-y-6">
                            <!-- Item 1 -->
                            <div class="flex group">
                                <div class="mr-4 flex-shrink-0">
                                    <div class="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-400 to-violet-500 flex items-center justify-center shadow-md group-hover:from-indigo-500 group-hover:to-violet-600 transition-all duration-300">
                                        <i class="fas fa-keyboard text-white"></i>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-gray-800 group-hover:text-indigo-600 transition-colors duration-300">Engage & Submit</h4>
                                    <p class="text-sm text-gray-600 mt-1">Work on problems with real-time guidance</p>
                                    <div class="flex items-center mt-2">
                                        <i class="far fa-clock text-indigo-400 mr-1"></i>
                                        <span class="text-xs font-medium text-indigo-600">Seconds to Minutes</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Item 2 -->
                            <div class="flex group">
                                <div class="mr-4 flex-shrink-0">
                                    <div class="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-400 to-violet-500 flex items-center justify-center shadow-md group-hover:from-indigo-500 group-hover:to-violet-600 transition-all duration-300">
                                        <i class="fas fa-robot text-white"></i>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-gray-800 group-hover:text-indigo-600 transition-colors duration-300">Instant AI Feedback</h4>
                                    <p class="text-sm text-gray-600 mt-1">Receive immediate, personalized guidance</p>
                                    <div class="flex items-center mt-2">
                                        <i class="far fa-clock text-indigo-400 mr-1"></i>
                                        <span class="text-xs font-medium text-indigo-600">Seconds</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Item 3 -->
                            <div class="flex group">
                                <div class="mr-4 flex-shrink-0">
                                    <div class="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-400 to-violet-500 flex items-center justify-center shadow-md group-hover:from-indigo-500 group-hover:to-violet-600 transition-all duration-300">
                                        <i class="fas fa-lightbulb text-white"></i>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-gray-800 group-hover:text-indigo-600 transition-colors duration-300">Learn & Apply</h4>
                                    <p class="text-sm text-gray-600 mt-1">Understand concepts and correct mistakes instantly</p>
                                    <div class="flex items-center mt-2">
                                        <i class="far fa-clock text-indigo-400 mr-1"></i>
                                        <span class="text-xs font-medium text-indigo-600">Seconds to Minutes</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Item 4 -->
                            <div class="flex group">
                                <div class="mr-4 flex-shrink-0">
                                    <div class="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-400 to-violet-500 flex items-center justify-center shadow-md group-hover:from-indigo-500 group-hover:to-violet-600 transition-all duration-300">
                                        <i class="fas fa-rocket text-white"></i>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-gray-800 group-hover:text-indigo-600 transition-colors duration-300">Achieve Mastery</h4>
                                    <p class="text-sm text-gray-600 mt-1">Rapidly iterate and build confidence</p>
                                    <div class="flex items-center mt-2">
                                        <i class="far fa-clock text-indigo-400 mr-1"></i>
                                        <span class="text-xs font-medium text-indigo-600">Continuous Progress</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Total Time -->
                        <div class="mt-8 pt-6 border-t border-indigo-100">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-500">Total Learning Cycle:</span>
                                <span class="text-lg font-bold text-indigo-600">Seconds to Minutes</span>
                            </div>
                            <div class="w-full h-2 bg-gray-100 rounded-full mt-2 overflow-hidden">
                                <div class="h-full bg-gradient-to-r from-indigo-400 to-violet-600 rounded-full" style="width: 15%"></div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2 text-right">Accelerated learning, immediate progress</p>
                        </div>
                    </div>
                </div>
                
                <!-- VS Badge (visible only on desktop) -->
                <div class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 hidden lg:flex items-center justify-center z-20">
                    <div class="w-12 h-12 rounded-full bg-white shadow-lg flex items-center justify-center">
                        <span class="text-sm font-bold text-gray-800">VS</span>
                    </div>
                </div>
            </div>
            
            <!-- Mobile VS Divider (visible only on mobile) -->
            <div class="relative my-8 flex items-center lg:hidden">
                <div class="flex-grow h-px bg-gradient-to-r from-red-300 to-transparent"></div>
                <div class="mx-4 w-10 h-10 rounded-full bg-white shadow-md flex items-center justify-center">
                    <span class="text-sm font-bold text-gray-800">VS</span>
                </div>
                <div class="flex-grow h-px bg-gradient-to-l from-indigo-300 to-transparent"></div>
            </div>
        </div>
    </div>
</div>

            <!-- Interactive Platform Demo -->
            <div class="mt-16">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">See VastLearn in Action</h2>
                    <p class="text-lg text-gray-600 max-w-2xl mx-auto">Experience our AI-powered learning platform with real-time feedback and intelligent marking</p>
                </div>

                <!-- Demo Interface Container -->
                <div class="relative bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 rounded-2xl p-8 shadow-2xl overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute inset-0 bg-grid-white/[0.05] bg-[size:20px_20px]"></div>

                    <!-- Floating Elements -->
                    <div class="absolute top-4 right-4 w-20 h-20 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-20 blur-xl animate-pulse"></div>
                    <div class="absolute bottom-4 left-4 w-16 h-16 bg-gradient-to-r from-green-400 to-blue-500 rounded-full opacity-20 blur-xl animate-pulse delay-1000"></div>

                    <!-- Demo Content -->
                    <div class="relative z-10">
                        <!-- Browser Window Mockup -->
                        <div class="bg-white rounded-lg shadow-2xl overflow-hidden">
                            <!-- Browser Header -->
                            <div class="bg-gray-100 px-4 py-3 flex items-center space-x-2 border-b">
                                <div class="flex space-x-2">
                                    <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                                    <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                </div>
                                <div class="flex-1 mx-4">
                                    <div class="bg-white rounded px-3 py-1 text-sm text-gray-600 border">
                                        <i class="fas fa-lock text-green-500 mr-2"></i>vastlearn.org/question/42
                                    </div>
                                </div>
                            </div>

                            <!-- Question Interface -->
                            <div class="p-6">
                                <!-- Question Header -->
                                <div class="flex justify-between items-start mb-6">
                                    <div>
                                        <h3 class="text-xl font-semibold text-gray-900">#42 [A-Level Chemistry] Halogen Properties</h3>
                                        <div class="flex items-center mt-2 space-x-4">
                                            <span class="text-sm text-gray-500">[2.0m]</span>
                                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Chemistry</span>
                                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Group 17</span>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm text-gray-500">Progress</div>
                                        <div class="text-lg font-semibold text-indigo-600">1 of 1</div>
                                    </div>
                                </div>

                                <!-- Question Content -->
                                <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                    <div class="prose prose-sm max-w-none">
                                        <p class="text-gray-800 mb-3">Chlorine is an element in Group 17 of the Periodic Table.</p>
                                        <p class="text-gray-900 font-medium">Describe and explain how the volatilities of the halogens vary from chlorine to iodine.</p>
                                    </div>
                                </div>

                                <!-- Answer Section -->
                                <div class="space-y-4">
                                    <div class="flex justify-between items-center">
                                        <h4 class="text-lg font-medium text-gray-900">Your Answer</h4>
                                        <button class="text-sm text-indigo-600 hover:text-indigo-800 flex items-center">
                                            <i class="fas fa-history mr-1"></i> View Past Submissions
                                        </button>
                                    </div>

                                    <!-- Answer Input with Highlights -->
                                    <div class="bg-white border-2 border-indigo-200 rounded-lg p-4 min-h-[120px] relative">
                                        <div class="text-gray-800 leading-relaxed">
                                            <span class="bg-yellow-100 px-1 rounded">The volatilities of halogens decrease down group 17.</span>
                                            Down group 17, the number of electron shells increase and thus
                                            <span class="bg-blue-100 px-1 rounded">the electron clouds become larger and more polarisable, forming stronger intermolecular forces</span>
                                            which require more energy to overcome. Hence, volatility decreases.
                                        </div>

                                        <!-- Submit Button -->
                                        <div class="mt-4 flex justify-end">
                                            <button class="bg-indigo-600 text-white px-6 py-2 rounded-md hover:bg-indigo-700 transition-colors flex items-center">
                                                <i class="fas fa-paper-plane mr-2"></i> Submit Answer
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- AI Feedback Section -->
                            <div class="bg-gradient-to-r from-green-50 to-blue-50 border-t p-6">
                                <div class="flex items-center mb-4">
                                    <div class="w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-robot text-white text-sm"></i>
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-900">AI Feedback</h4>
                                    <div class="ml-auto flex items-center">
                                        <span class="text-2xl font-bold text-green-600">2.0/2.0</span>
                                        <span class="text-sm text-gray-500 ml-2">Perfect Score!</span>
                                    </div>
                                </div>

                                <!-- Marking Points -->
                                <div class="space-y-4">
                                    <!-- Point 1 -->
                                    <div class="flex items-start space-x-3">
                                        <div class="w-1 h-16 bg-yellow-400 rounded-full flex-shrink-0"></div>
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-2 mb-2">
                                                <i class="fas fa-check-circle text-green-500"></i>
                                                <span class="font-semibold text-green-800">[1.0/1.0 marks]</span>
                                            </div>
                                            <p class="text-sm text-gray-700 mb-1">
                                                <strong>Your answer:</strong> <span class="bg-yellow-100 px-1 rounded">The volatilities of halogens decrease down group 17.</span>
                                            </p>
                                            <p class="text-xs text-gray-600">✓ Correctly identified the trend in volatility</p>
                                        </div>
                                    </div>

                                    <!-- Point 2 -->
                                    <div class="flex items-start space-x-3">
                                        <div class="w-1 h-16 bg-blue-400 rounded-full flex-shrink-0"></div>
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-2 mb-2">
                                                <i class="fas fa-check-circle text-green-500"></i>
                                                <span class="font-semibold text-green-800">[1.0/1.0 marks]</span>
                                            </div>
                                            <p class="text-sm text-gray-700 mb-1">
                                                <strong>Your answer:</strong> <span class="bg-blue-100 px-1 rounded">electron clouds become larger and more polarisable, forming stronger intermolecular forces</span>
                                            </p>
                                            <p class="text-xs text-gray-600">✓ Excellent explanation of the underlying mechanism</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="mt-8 flex justify-center space-x-4">
                            <button id="demo-replay" class="bg-white/10 backdrop-blur-sm text-white px-6 py-3 rounded-lg hover:bg-white/20 transition-all duration-200 flex items-center border border-white/20">
                                <i class="fas fa-play mr-2"></i> Watch Demo Again
                            </button>
                            <a href="{{ url_for('register') }}" class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-3 rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 flex items-center shadow-lg">
                                <i class="fas fa-rocket mr-2"></i> Start Learning Now
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Feature Highlights -->
                <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                        <div class="flex items-center mb-3">
                            <div class="w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-check-circle text-white"></i>
                            </div>
                            <h4 class="text-white font-semibold">Instant AI Marking</h4>
                        </div>
                        <p class="text-gray-300 text-sm">Get immediate feedback with detailed marking points and explanations</p>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                        <div class="flex items-center mb-3">
                            <div class="w-10 h-10 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-highlight text-white"></i>
                            </div>
                            <h4 class="text-white font-semibold">Smart Highlighting</h4>
                        </div>
                        <p class="text-gray-300 text-sm">See exactly which parts of your answer earned marks</p>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                        <div class="flex items-center mb-3">
                            <div class="w-10 h-10 bg-gradient-to-r from-green-400 to-blue-500 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-chart-line text-white"></i>
                            </div>
                            <h4 class="text-white font-semibold">Progress Tracking</h4>
                        </div>
                        <p class="text-gray-300 text-sm">Monitor your improvement across all subjects and topics</p>
                    </div>
                </div>
            </div>

            <!-- Demo JavaScript -->
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const replayButton = document.getElementById('demo-replay');

                    if (replayButton) {
                        replayButton.addEventListener('click', function() {
                            // Add a subtle animation effect when replay is clicked
                            const demoContainer = replayButton.closest('.relative');
                            demoContainer.style.transform = 'scale(0.98)';
                            setTimeout(() => {
                                demoContainer.style.transform = 'scale(1)';
                            }, 150);

                            // You could add more demo interactions here
                            console.log('Demo replay clicked');
                        });
                    }
                });
            </script>
        </div>

<!-- Transformation Journey Section -->
<div class="mx-auto max-w-7xl px-6 lg:px-8 py-24 sm:py-32">
    <div class="mx-auto max-w-2xl lg:text-center opacity-0 translate-y-4 transition-all duration-1000" id="features-header">
        <h2 class="text-base font-semibold leading-7 text-indigo-600">Your Path to Excellence</h2>
        <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Overcome Your Academic Challenges</p>
        <p class="mt-6 text-lg leading-8 text-gray-600">We've helped thousands of struggling students transform their grades and confidence. Here's how we'll help you rise to the top of your class.</p>
    </div>
    <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
        <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
            <div class="flex flex-col opacity-0 translate-y-4 transition-all duration-1000 group" id="feature-1">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <div class="rounded-lg bg-indigo-600 p-2 ring-1 ring-indigo-600/10 group-hover:bg-indigo-500 transition-colors duration-200">
                        <i class="fas fa-brain text-white"></i>
                    </div>
                    Overcome Confusion
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p class="flex-auto">Turn frustration into understanding with our step-by-step explanations that break down complex concepts into simple, digestible pieces.</p>

                    <p class="mt-6">
                        <a href="#" class="text-sm font-semibold leading-6 text-indigo-600 group-hover:text-indigo-500 transition-colors duration-200">
                            See how it works <span aria-hidden="true">→</span>
                        </a>
                    </p>
                </dd>
            </div>
            <div class="flex flex-col opacity-0 translate-y-4 transition-all duration-1000 group" id="feature-2">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <div class="rounded-lg bg-indigo-600 p-2 ring-1 ring-indigo-600/10 group-hover:bg-indigo-500 transition-colors duration-200">
                        <i class="fas fa-chart-line text-white"></i>
                    </div>
                    Build Confidence
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p class="flex-auto">Watch your confidence grow as you master each concept and see your progress visualized. No more feeling lost or left behind.</p>

                    <p class="mt-6">
                        <a href="#" class="text-sm font-semibold leading-6 text-indigo-600 group-hover:text-indigo-500 transition-colors duration-200">
                            Track your growth <span aria-hidden="true">→</span>
                        </a>
                    </p>
                </dd>
            </div>
            <div class="flex flex-col opacity-0 translate-y-4 transition-all duration-1000 group" id="feature-3">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <div class="rounded-lg bg-indigo-600 p-2 ring-1 ring-indigo-600/10 group-hover:bg-indigo-500 transition-colors duration-200">
                        <i class="fas fa-trophy text-white"></i>
                    </div>
                    Achieve Excellence
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p class="flex-auto">Transform from struggling to excelling with personalized learning paths that adapt to your needs and help you reach your full potential.</p>

                    <p class="mt-6">
                        <a href="#" class="text-sm font-semibold leading-6 text-indigo-600 group-hover:text-indigo-500 transition-colors duration-200">
                            Start your journey <span aria-hidden="true">→</span>
                        </a>
                    </p>
                </dd>
            </div>
        </dl>
    </div>
</div>

<!-- Transformation Tools Section -->
<div class="bg-white py-24 sm:py-32">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl lg:text-center opacity-0 translate-y-4 transition-all duration-1000" id="highlights-header">
            <h2 class="text-base font-semibold leading-7 text-indigo-600">Your Academic Transformation</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Tools That Turn F's into A's</p>
            <p class="mt-6 text-lg leading-8 text-gray-600">Our proven system has helped thousands of struggling students achieve remarkable grade improvements.</p>
        </div>

        <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none lg:grid lg:grid-cols-2 lg:gap-8 opacity-0 translate-y-4 transition-all duration-1000" id="highlights-content">
            <!-- Personalized Learning -->
            <div class="relative bg-white p-8 shadow-lg ring-1 ring-gray-900/5 rounded-2xl overflow-hidden transform hover:-translate-y-1 transition-all duration-300">
                <div class="absolute inset-0 bg-gradient-to-br from-indigo-50 to-white opacity-50"></div>
                <div class="relative">
                    <div class="flex items-center justify-center w-12 h-12 bg-indigo-600 rounded-lg mb-6">
                        <i class="fas fa-user-graduate text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Personalized Learning Path</h3>
                    <ul class="space-y-3 text-gray-600">
                        <li class="flex items-center">
                            <i class="fas fa-check text-indigo-600 mr-2"></i>
                            <span>Identifies and targets <strong class="text-indigo-700">your specific weaknesses</strong></span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-indigo-600 mr-2"></i>
                            <span>Adapts difficulty based on <strong class="text-indigo-700">your progress</strong></span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-indigo-600 mr-2"></i>
                            <span>Builds confidence through <strong class="text-indigo-700">achievable challenges</strong></span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-indigo-600 mr-2"></i>
                            <span>Creates a <strong class="text-indigo-700">roadmap to excellence</strong> for your studies</span>
                        </li>
                    </ul>

                </div>
            </div>

            <!-- Breakthrough Methods -->
            <div class="relative bg-white p-8 shadow-lg ring-1 ring-gray-900/5 rounded-2xl overflow-hidden transform hover:-translate-y-1 transition-all duration-300 mt-8 lg:mt-0">
                <div class="absolute inset-0 bg-gradient-to-br from-violet-50 to-white opacity-50"></div>
                <div class="relative">
                    <div class="flex items-center justify-center w-12 h-12 bg-violet-600 rounded-lg mb-6">
                        <i class="fas fa-lightbulb text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Breakthrough Learning Methods</h3>
                    <ul class="space-y-3 text-gray-600">
                        <li class="flex items-center">
                            <i class="fas fa-check text-violet-600 mr-2"></i>
                            <span>Transforms complex topics into <strong class="text-violet-700">simple building blocks</strong></span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-violet-600 mr-2"></i>
                            <span>Provides <strong class="text-violet-700">instant, detailed feedback</strong> on your work</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-violet-600 mr-2"></i>
                            <span>Uses <strong class="text-violet-700">proven memory techniques</strong> for better retention</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-violet-600 mr-2"></i>
                            <span>Teaches you <strong class="text-violet-700">how to think</strong>, not just memorize</span>
                        </li>
                    </ul>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- Final Call to Action Section -->
<div class="bg-gradient-to-b from-white to-gray-50">
    <div class="mx-auto max-w-7xl px-6 lg:px-8 py-24 sm:py-32">
        <div class="mx-auto max-w-2xl lg:text-center opacity-0 translate-y-4 transition-all duration-1000" id="community-header">
            <h2 class="text-base font-semibold leading-7 text-indigo-600">Your Future Awaits</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Begin Your Transformation Today</p>
            <p class="mt-6 text-lg leading-8 text-gray-600">Join thousands of students who went from struggling to excelling. Your academic breakthrough is just one click away.</p>
        </div>
        <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none opacity-0 translate-y-4 transition-all duration-1000" id="community-content">
            <div class="bg-white shadow-xl rounded-2xl overflow-hidden">
                <div class="grid grid-cols-1 lg:grid-cols-2">
                    <!-- Transformation Image -->
                    <div class="relative bg-gradient-to-br from-indigo-500 to-purple-600 p-8 flex items-center justify-center">
                        <div class="absolute inset-0 opacity-10">
                            <div class="grid grid-cols-4 gap-4 h-full">
                                <i class="fas fa-arrow-up text-4xl text-white self-center justify-self-center animate-float"></i>
                                <i class="fas fa-trophy text-4xl text-white self-center justify-self-center animate-float-delayed"></i>
                                <i class="fas fa-star text-4xl text-white self-center justify-self-center animate-float"></i>
                                <i class="fas fa-medal text-4xl text-white self-center justify-self-center animate-float-delayed"></i>
                                <i class="fas fa-chart-line text-4xl text-white self-center justify-self-center animate-float-delayed"></i>
                                <i class="fas fa-graduation-cap text-4xl text-white self-center justify-self-center animate-float"></i>
                                <i class="fas fa-lightbulb text-4xl text-white self-center justify-self-center animate-float-delayed"></i>
                                <i class="fas fa-award text-4xl text-white self-center justify-self-center animate-float"></i>
                            </div>
                        </div>
                        <div class="relative text-center text-white">
                            <div class="flex items-center justify-center mb-6">
                                <div class="w-20 h-20 rounded-full bg-white/20 flex items-center justify-center backdrop-blur-sm">
                                    <i class="fas fa-user-graduate text-4xl text-white"></i>
                                </div>
                            </div>
                            <h3 class="text-2xl font-bold mb-2">From Struggling to Succeeding</h3>
                            <div class="flex items-center justify-center space-x-2 mb-4">
                                <div class="h-3 w-20 bg-red-400/70 rounded-full"></div>
                                <i class="fas fa-arrow-right text-white/70"></i>
                                <div class="h-3 w-20 bg-yellow-400/70 rounded-full"></div>
                                <i class="fas fa-arrow-right text-white/70"></i>
                                <div class="h-3 w-20 bg-green-400/70 rounded-full"></div>
                            </div>
                            <p class="text-white/80 max-w-md mx-auto">Your journey to academic excellence starts with a single step. Join the thousands of students who transformed their grades and confidence.</p>
                        </div>
                    </div>

                    <!-- CTA Form -->
                    <div class="p-8 lg:p-12">
                        <h3 class="text-xl font-semibold text-gray-900 mb-6">Start Your Academic Transformation</h3>
                        <div class="space-y-6">
                            <div>
                                <p class="text-sm text-gray-600 mb-4">Join our community of students who went from:</p>
                                <ul class="space-y-3">
                                    <li class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-times text-red-500 mr-2"></i>
                                        <span>Struggling with difficult concepts</span>
                                        <i class="fas fa-arrow-right text-gray-400 mx-2"></i>
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        <span class="text-green-700 font-medium">Mastering complex topics</span>
                                    </li>
                                    <li class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-times text-red-500 mr-2"></i>
                                        <span>Feeling lost and confused</span>
                                        <i class="fas fa-arrow-right text-gray-400 mx-2"></i>
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        <span class="text-green-700 font-medium">Confident and prepared</span>
                                    </li>
                                    <li class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-times text-red-500 mr-2"></i>
                                        <span>Failing grades and tests</span>
                                        <i class="fas fa-arrow-right text-gray-400 mx-2"></i>
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        <span class="text-green-700 font-medium">Top of the class</span>
                                    </li>
                                </ul>
                            </div>

                            <div class="pt-4">
                                <a href="{{ url_for('register') }}" class="w-full flex items-center justify-center rounded-md bg-indigo-600 px-5 py-4 text-base font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transition-all duration-200 transform hover:scale-105">
                                    Begin Your Transformation
                                    <i class="fas fa-arrow-right ml-2"></i>
                                </a>
                                <p class="mt-4 text-center text-sm text-gray-500">
                                    Already a member? <a href="{{ url_for('login') }}" class="font-semibold leading-6 text-indigo-600 hover:text-indigo-500">Sign in</a> to continue your journey.
                                </p>
                            </div>

                            {% if active_users %}
                            <div class="pt-4 border-t border-gray-200">
                                <p class="text-xs text-gray-500 mb-2 text-center">Join these students who are transforming their grades right now:</p>
                                <div class="flex flex-wrap gap-2 justify-center">
                                    {% for user in active_users %}
                                    <a href="{{ url_for('user_profile', username=user.username) }}" class="inline-flex items-center rounded-full bg-indigo-50 px-3 py-1 text-xs font-medium text-indigo-700 ring-1 ring-inset ring-indigo-700/10 hover:bg-indigo-100 transition-all duration-200 transform hover:scale-105">
                                        <i class="fas fa-user-circle text-sm mr-1"></i>
                                        {{ user.username }}
                                    </a>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Add smooth scroll behavior
document.documentElement.style.scrollBehavior = 'smooth';

// Enhanced animations
const animatedElements = [
    'hero-content',
    'hero-image',
    'how-it-works-header', // Added for timeline visibility
    'how-it-works-content', // Added for timeline visibility
    'cta-section',
    'faq-header',
    'faq-content',
    'showcase-header',
    'showcase-content',
    'features-header',
    'feature-1',
    'feature-2',
    'feature-3',
    'highlights-header',
    'highlights-content',
    'community-header',
    'community-content'
];

// Initial animations with stagger
setTimeout(() => {
    document.getElementById('hero-content').classList.remove('opacity-0', 'translate-y-4');
    setTimeout(() => {
        document.getElementById('hero-image').classList.remove('opacity-0', 'translate-x-4');
    }, 200);
}, 100);

// Scroll animations with stagger
const observerOptions = {
    root: null,
    rootMargin: '0px',
    threshold: 0.1
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry, index) => {
        if (entry.isIntersecting) {
            setTimeout(() => {
                entry.target.classList.remove('opacity-0', 'translate-y-4');
            }, index * 100);
        }
    });
}, observerOptions);

animatedElements.forEach(id => {
    const element = document.getElementById(id);
    if (element) {
        observer.observe(element);
    }
});

// Enhanced parallax effect
let ticking = false;
window.addEventListener('scroll', () => {
    if (!ticking) {
        window.requestAnimationFrame(() => {
            const scrolled = window.pageYOffset;
            const heroContent = document.getElementById('hero-content');
            const heroImage = document.getElementById('hero-image');

            if (heroContent && heroImage) {
                heroContent.style.transform = `translateY(${scrolled * 0.1}px)`;
                heroImage.style.transform = `translate3d(${scrolled * 0.05}px, ${scrolled * 0.02}px, 0) rotate(${scrolled * 0.02}deg)`;
            }
            ticking = false;
        });
        ticking = true;
    }
});

// Add hover effects to the showcase cards
const showcaseCards = document.querySelectorAll('.showcase-card');
if (showcaseCards) {
    showcaseCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.classList.add('transform', 'scale-105');
            card.querySelector('.card-icon').classList.add('animate-pulse');
        });

        card.addEventListener('mouseleave', () => {
            card.classList.remove('transform', 'scale-105');
            card.querySelector('.card-icon').classList.remove('animate-pulse');
        });
    });
}

// Showcase section animation
const showcaseObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const cards = entry.target.querySelectorAll('.relative');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('animate-fade-in');
                }, index * 200);
            });
            showcaseObserver.unobserve(entry.target);
        }
    });
}, { threshold: 0.2 });

const showcaseContent = document.getElementById('showcase-content');
if (showcaseContent) {
    showcaseObserver.observe(showcaseContent);
}
</script>

<style>
@keyframes grid {
    to {
        transform: translateY(-1rem);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes fade-in {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-float-delayed {
    animation: float 3s ease-in-out infinite;
    animation-delay: 1.5s;
}

.animate-fade-in {
    animation: fade-in 0.8s ease-out forwards;
}

.glow {
    text-shadow: 0 0 20px rgba(67, 56, 202, 0.4),
                 0 0 40px rgba(109, 40, 217, 0.3),
                 0 0 60px rgba(147, 51, 234, 0.2);
    transform: scale(1.01);
}

.hover\:glow:hover {
    text-shadow: 0 0 20px rgba(67, 56, 202, 0.4),
                 0 0 40px rgba(109, 40, 217, 0.3),
                 0 0 60px rgba(147, 51, 234, 0.2);
    transform: scale(1.01);
}

/* Grid pattern for demo background */
.bg-grid-white\/\[0\.05\] {
    background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.05) 1px, transparent 0);
}

/* Backdrop blur utilities */
.backdrop-blur-sm {
    backdrop-filter: blur(4px);
}

/* Animation for demo elements */
@keyframes demo-pulse {
    0%, 100% {
        opacity: 0.2;
    }
    50% {
        opacity: 0.4;
    }
}

.animate-demo-pulse {
    animation: demo-pulse 3s ease-in-out infinite;
}

</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const isLoggedIn = {{ session.get('user_id') is not none | tojson }};

        if (isLoggedIn) {
            const startTourButton = document.getElementById('start-vault-tour-button');
            if (startTourButton) {
                startTourButton.addEventListener('click', function() {
                    // Clear all tour completion flags to restart the entire tour
                    localStorage.removeItem('globalTourCompleted');
                    localStorage.removeItem('vaultShepherdTourCompleted');
                    localStorage.removeItem('dojoShepherdTourCompleted');
                    window.location.href = "{{ url_for('vault') }}?start_tour=true"; // Redirect to vault to start tour
                });
            }
        }
    });
</script>

{% endblock %}
