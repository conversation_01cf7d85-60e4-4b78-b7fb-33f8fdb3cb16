// Function to underline missed content in marking point description
function underlineMissedContent(description, missedSnippets) {
    if (!missedSnippets || missedSnippets.length === 0) {
        return description;
    }

    let result = description;

    // Process each missed snippet
    missedSnippets.forEach(snippet => {
        if (snippet && snippet.trim()) {
            // Create a case-insensitive regex to find the snippet
            const escapedSnippet = snippet.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            const regex = new RegExp(`(${escapedSnippet})`, 'gi');

            // Replace with underlined version
            result = result.replace(regex, '<u class="text-red-600 decoration-red-600 decoration-2">$1</u>');
        }
    });

    return result;
}

function showLoading(event, partId) {
    event.preventDefault();
    const form = event.target;
    const loading = document.getElementById(`loading_${partId}`);
    const correctAnswer = document.getElementById(`correct_answer_${partId}`);

    loading.classList.remove('hidden');
    correctAnswer.classList.remove('hidden');

    return false; // Prevent form submission as we're using submitAllAnswers
}

// Add MathQuill initialization
const MQ = MathQuill.getInterface(2);
const mathFields = {};

document.addEventListener('DOMContentLoaded', function() {
    // Initialize MathQuill fields
    document.querySelectorAll('form[data-part-id]').forEach(form => {
        const partId = form.getAttribute('data-part-id');
        const mathInput = document.getElementById(`math_input_${partId}`);
        if (mathInput) {
            mathFields[partId] = MQ.MathField(mathInput, {
                handlers: {
                    edit: function() {
                        // Don't update textarea directly anymore, only handle the math input
                        const latex = mathFields[partId].latex();
                        // Store the latex in a data attribute for later use
                        mathInput.dataset.currentLatex = latex;
                    }
                }
            });
        }
    });

    // Add submit handlers to each form
    document.querySelectorAll('form[data-part-id]').forEach(form => {
        const partId = form.getAttribute('data-part-id');
        form.addEventListener('submit', (event) => submitPart(event, partId));
    });

    // Add toggle handlers for submissions
    document.querySelectorAll('.toggle-submissions').forEach(button => {
        button.addEventListener('click', function() {
            const partId = this.getAttribute('data-part-id');
            const submissionsList = document.getElementById(`submissions-${partId}`);

            if (submissionsList.classList.contains('hidden')) {
                submissionsList.classList.remove('hidden');
                this.textContent = 'Hide';
            } else {
                submissionsList.classList.add('hidden');
                this.textContent = 'Show All';
            }
        });
    });

    // Add event listener to save cursor position when clicking in textarea
    document.querySelectorAll('textarea[id^="answer_"]').forEach(textarea => {
        textarea.addEventListener('click', function() {
            const partId = this.id.split('_')[1];
            const calculator = document.getElementById(`calculator_${partId}`);
            if (calculator) {
                calculator.dataset.cursorPosition = this.selectionStart;
            }
        });

        textarea.addEventListener('keyup', function() {
            const partId = this.id.split('_')[1];
            const calculator = document.getElementById(`calculator_${partId}`);
            if (calculator) {
                calculator.dataset.cursorPosition = this.selectionStart;
            }
        });
    });
});

// Function to insert math symbols
function insertMath(partId, symbol) {
    const mathField = mathFields[partId];
    if (mathField) {
        if (symbol === '\\sqrt' || symbol === '\\frac' ) {
            mathField.cmd(symbol);
        } else if (symbol === '^') {
            mathField.cmd('^');
            mathField.typedText('');
        } else {
            mathField.write(symbol);
        }
        mathField.focus();
    }
}
// Function to insert to textarea at cursor position
function insertToTextarea(partId) {
    const mathField = mathFields[partId];
    const textarea = document.getElementById(`answer_${partId}`);

    if (mathField && textarea) {
        const latex = mathField.latex();
        if (!latex) return; // Don't insert if empty

        // Save the current cursor position or use the end of text
        const savedStart = textarea.selectionStart || textarea.value.length;
        const savedEnd = textarea.selectionEnd || textarea.value.length;

        // Format the LaTeX for display
        let displayText = latex;
        // If it's a simple expression (no special LaTeX commands), keep it as is
        if (!latex.includes('\\')) {
            displayText = latex;
        } else {
            // Wrap LaTeX expressions in $$ for proper display
            displayText = `$${latex}$`;
        }

        // Insert at cursor position or append to end
        const textBefore = textarea.value.substring(0, savedStart);
        const textAfter = textarea.value.substring(savedEnd);

        // Add spaces around the expression if needed
        const needSpaceBefore = textBefore.length > 0 && !textBefore.endsWith(' ');
        const needSpaceAfter = textAfter.length > 0 && !textAfter.startsWith(' ');

        const newText = textBefore +
                      (needSpaceBefore ? ' ' : '') +
                      displayText +
                      (needSpaceAfter ? ' ' : '') +
                      textAfter;

        // Update textarea while preserving existing content
        textarea.value = newText;

        // Calculate and restore cursor position after the inserted expression
        const newCursorPos = savedStart +
                           (needSpaceBefore ? 1 : 0) +
                           displayText.length +
                           (needSpaceAfter ? 1 : 0);

        textarea.focus();
        textarea.selectionStart = newCursorPos;
        textarea.selectionEnd = newCursorPos;

        // Clear the math input for next expression
        mathField.latex('');
    }
}

// Function to toggle calculator
function toggleCalculator(partId) {
    const calculator = document.getElementById(`calculator_${partId}`);
    const toggleText = document.getElementById(`calc_toggle_text_${partId}`);
    const textarea = document.getElementById(`answer_${partId}`);

    if (calculator.classList.contains('hidden')) {
        calculator.classList.remove('hidden');
        toggleText.textContent = 'Hide Calculator';

        // Save cursor position when opening calculator
        calculator.dataset.cursorPosition = textarea.selectionStart || textarea.value.length;
    } else {
        calculator.classList.add('hidden');
        toggleText.textContent = 'Show Calculator';

        // Restore focus to textarea when closing calculator
        textarea.focus();
        const savedPos = parseInt(calculator.dataset.cursorPosition) || textarea.value.length;
        textarea.selectionStart = savedPos;
        textarea.selectionEnd = savedPos;
    }
}

// Function to submit a single part
async function submitPart(event, partId) {
    event.preventDefault();
    const form = event.target;

    const submitButton = document.getElementById(`submit-part-${partId}`);
    const loading = document.getElementById(`loading_${partId}`);
    const correctAnswer = document.getElementById(`correct_answer_${partId}`);

    // Disable the submit button and show loading
    submitButton.disabled = true;
    loading.classList.remove('hidden');

    try {
        const formData = new FormData(form);
        formData.append('confidence_level', 'Medium'); // Changed from 'confidence' to 'confidence_level'

        const response = await fetch(form.action, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        // Show feedback for this part
        const feedbackDiv = document.querySelector(`#feedback_${partId}`);
        if (feedbackDiv && result.feedback) {
            // Add verdict and score display
            const verdictHtml = `
                <div class="mb-4 flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <span class="text-sm font-medium">Verdict:</span>
                        <span class="text-sm font-semibold ${result.score == result.max_score ? 'text-green-600' : result.score > 0 ? 'text-yellow-600' : 'text-red-600'}">
                            ${result.score == result.max_score ? 'Correct' : result.score > 0 ? 'Partial' : 'Incorrect'}
                        </span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm font-medium">Score:</span>
                        <span class="text-sm font-semibold ${result.score >= result.max_score ? 'text-green-600' : result.score > 0 ? 'text-yellow-600' : 'text-red-600'}">
                            ${result.score}/${result.max_score}
                        </span>
                    </div>
                </div>
            `;
            feedbackDiv.querySelector('.prose').innerHTML = verdictHtml + result.feedback;
            feedbackDiv.classList.remove('hidden');
        }

        // Show correct answer for this part if the answer was not irrelevant
        if (correctAnswer && !result.feedback.includes('irrelevant')) {
            correctAnswer.classList.remove('hidden');
        }

    } catch (error) {
        console.error('Error submitting answer:', error);
    } finally {
        // Hide loading and re-enable submit button
        loading.classList.add('hidden');
        submitButton.disabled = false;
    }
}

async function submitAllAnswers(event) {
    event.preventDefault();
    console.log("submitAllAnswers called");
    const submitAllButton = document.getElementById('submit-all-button');
    if (!submitAllButton) {
        console.error("Submit all button not found");
        return;
    }

    submitAllButton.disabled = true;

    // Find all forms with data-part-id attribute
    const forms = document.querySelectorAll('form[data-part-id]');
    console.log(`Found ${forms.length} forms to submit`);

    if (forms.length === 0) {
        alert("No answer forms found to submit");
        submitAllButton.disabled = false;
        return;
    }

    // Show all loading indicators
    forms.forEach(form => {
        const partId = form.getAttribute('data-part-id');
        const loading = document.getElementById(`loading_${partId}`);
        if (loading) loading.classList.remove('hidden');
    });

    try {
        // Create array of promises for all form submissions
        const submissionPromises = Array.from(forms).map(async form => {
            const partId = form.getAttribute('data-part-id');
            const textarea = form.querySelector('textarea[name="answer"]');

            if (!textarea) {
                console.error(`No textarea found for part ${partId}`);
                return null;
            }

            const answer = textarea.value.trim();
            if (!answer) {
                console.log(`No answer provided for part ${partId}, skipping`);
                return null;
            }

            console.log(`Submitting answer for part ${partId}`);

            try {
                // Create a new FormData object
                const formData = new FormData();
                formData.append('answer', answer);
                formData.append('confidence_level', 'Medium');

                // Get any file inputs
                const fileInput = form.querySelector('input[type="file"]');
                if (fileInput && fileInput.files.length > 0) {
                    formData.append('image', fileInput.files[0]);
                }

                const response = await fetch(form.action, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error(`Error response for part ${partId}:`, errorText);
                    return {
                        partId,
                        error: `HTTP error ${response.status}`,
                        result: null
                    };
                }

                const result = await response.json();
                console.log(`Received result for part ${partId}:`, result);

                return {
                    partId,
                    result
                };
            } catch (error) {
                console.error(`Error submitting part ${partId}:`, error);
                return {
                    partId,
                    error: error.message,
                    result: null
                };
            }
        });

        // Wait for all submissions to complete
        console.log("Waiting for all submissions to complete...");
        const results = await Promise.allSettled(submissionPromises);
        let totalSubmitted = 0;
        let errors = 0;

        // Process all results
        results.forEach(submission => {
            if (submission.status === 'rejected') {
                console.error("Promise rejected:", submission.reason);
                errors++;
                return;
            }

            if (!submission.value || submission.value.error) {
                if (submission.value) {
                    console.error("Submission error:", submission.value.error);
                    errors++;
                }
                return;
            }

            const { partId, result } = submission.value;
            if (!result) return;

            // Handle the answer panels if they exist
            const panelsDiv = document.getElementById(`answer_panels_${partId}`);
            if (panelsDiv && result.status === 'success') {
                panelsDiv.classList.remove('hidden');

                // Get the panels
                const userAnswerDiv = document.getElementById(`user_answer_${partId}`);
                const markingSchemeDiv = document.getElementById(`marking_scheme_${partId}`);

                if (userAnswerDiv && result.answer) {
                    userAnswerDiv.innerHTML = result.answer;
                }

                if (markingSchemeDiv && result.marking_points) {
                    // Display the marking scheme
                    markingSchemeDiv.innerHTML = result.marking_points.map((mp, index) => {
                        const borderClass = mp.color || '';
                        return `
                        <div class="grid-row flex items-start p-3 rounded bg-gray-50 h-full transform transition-all duration-300 hover:bg-gray-100 hover:shadow-sm">
                            <!-- Vertical Color Bar -->
                            <div class="flex-shrink-0 w-1 self-stretch rounded-l-md mr-3 ${borderClass ? borderClass.replace('border-', 'bg-').replace('-400', '-300') : 'bg-transparent'}"></div>

                            <div class="flex-grow flex items-start space-x-3">
                                <!-- Achieved/Not Achieved Icon -->
                                <div class="flex-shrink-0 mt-0.5">
                                    <span class="flex items-center justify-center h-5 w-5 rounded-full ${mp.achieved ? 'bg-green-100 text-green-600' : mp.partial ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-100 text-gray-400'}">
                                        ${mp.achieved ?
                                            '<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>' :
                                            mp.partial ?
                                            '<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z" clip-rule="evenodd"></path></svg>' :
                                            '<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd"></path></svg>'
                                        }
                                    </span>
                                </div>
                                <!-- Marking Point Text -->
                                <div class="flex-1">
                                    <p class="text-sm font-medium ${mp.achieved ? 'text-green-800' : mp.partial ? 'text-yellow-800' : 'text-red-800'}">
                                        [${mp.achieved_score}/${mp.score} marks]
                                        ${mp.partial ? '<span class="ml-1 text-xs text-yellow-600 font-medium">(Partial Credit)</span>' : ''}
                                    </p>
                                    ${mp.feedback ? `
                                        <div class="mt-2 text-sm font-medium ${mp.achieved ? 'text-green-700' : mp.partial ? 'text-yellow-700' : 'text-red-700'} bg-white px-3 py-2 rounded border-l-4 ${mp.achieved ? 'border-green-400' : mp.partial ? 'border-yellow-400' : 'border-red-400'}">
                                            <i class="fas ${mp.achieved ? 'fa-check-circle' : mp.partial ? 'fa-exclamation-triangle' : 'fa-times-circle'} mr-2"></i>
                                            ${mp.feedback}
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>`;
                    }).join('');
                }

                totalSubmitted++;
            }

            // Handle the feedback div if it exists
            const feedbackDiv = document.getElementById(`feedback-${partId}`);
            if (feedbackDiv && result.status === 'success') {
                // Generate detailed feedback HTML based on marking points
                let feedbackHtml = `
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Feedback Breakdown</h4>
                    <div class="space-y-3">
                `;

                if (result.marking_points && result.marking_points.length > 0) {
                    result.marking_points.forEach(mp => {
                        feedbackHtml += `
                            <div class="flex items-start space-x-3 p-3 rounded ${mp.achieved ? 'bg-green-50' : mp.partial ? 'bg-yellow-50' : 'bg-red-50'} border ${mp.achieved ? 'border-green-200' : mp.partial ? 'border-yellow-200' : 'border-red-200'} marking-point-mobile">
                                <div class="flex-shrink-0 pt-1">
                                    ${mp.achieved
                                        ? '<svg class="h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>'
                                        : mp.partial
                                        ? '<svg class="h-5 w-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z"/></svg>'
                                        : '<svg class="h-5 w-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/></svg>'
                                    }
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium ${mp.achieved ? 'text-green-800' : mp.partial ? 'text-yellow-800' : 'text-red-800'}">
                                        [${mp.achieved_score}/${mp.score} marks]
                                        ${mp.partial ? '<span class="ml-1 text-xs text-yellow-600 font-medium">(Partial Credit)</span>' : ''}
                                    </p>
                                    ${mp.feedback ? `
                                        <div class="mt-2 text-sm font-medium ${mp.achieved ? 'text-green-700' : mp.partial ? 'text-yellow-700' : 'text-red-700'} bg-white px-3 py-2 rounded border-l-4 ${mp.achieved ? 'border-green-400' : mp.partial ? 'border-yellow-400' : 'border-red-400'}">
                                            <i class="fas ${mp.achieved ? 'fa-check-circle' : mp.partial ? 'fa-exclamation-triangle' : 'fa-times-circle'} mr-2"></i>
                                            ${mp.partial && mp.missed_snippets ? underlineMissedContent(mp.feedback, mp.missed_snippets) : mp.feedback}
                                        </div>
                                    ` : ''}
                                    ${mp.evidence ? `
                                        <div class="mt-1 text-xs ${mp.achieved ? 'text-green-700' : mp.partial ? 'text-yellow-700' : 'text-red-700'} bg-white bg-opacity-60 p-2 rounded border ${mp.achieved ? 'border-green-200' : mp.partial ? 'border-yellow-200' : 'border-red-200'}">
                                            <strong>Evidence:</strong> "${mp.evidence}"
                                        </div>
                                    ` : (mp.achieved || mp.partial) ? '' : '<p class="mt-1 text-xs text-red-700 italic">No relevant evidence found in answer.</p>'}
                                    ${mp.partial && mp.missed ? `
                                        <div class="mt-1 text-xs text-red-700 bg-red-50 p-2 rounded border border-red-200">
                                            <strong>Missing from answer:</strong> "${mp.missed}"
                                        </div>
                                    ` : ''}
                                    ${mp.error ? `<p class="mt-1 text-xs text-red-700 font-semibold">Error during evaluation.</p>` : ''}
                                </div>
                            </div>
                        `;
                    });
                } else {
                     feedbackHtml += `<p class="text-sm text-gray-600 italic">No marking points defined for this part.</p>`;
                }

                feedbackHtml += `</div>`; // Close space-y-3

                // Add total score summary
                const totalScore = result.score || 0;
                const maxScore = result.max_score || 0;

                feedbackHtml += `
                    <div class="mt-4 pt-4 border-t border-gray-200 flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-900">Total Score:</span>
                        <span class="text-lg font-bold ${totalScore === maxScore ? 'text-green-600' : totalScore > 0 ? 'text-yellow-600' : 'text-red-600'}">
                            ${totalScore} / ${maxScore}
                        </span>
                    </div>
                `;

                feedbackDiv.innerHTML = feedbackHtml;
                feedbackDiv.classList.remove('hidden');

                // Update background based on overall score
                if (totalScore === maxScore) {
                    feedbackDiv.className = 'mt-6 rounded-xl p-6 transform transition-all duration-300 bg-green-50 border border-green-200';
                } else if (totalScore > 0) {
                    feedbackDiv.className = 'mt-6 rounded-xl p-6 transform transition-all duration-300 bg-yellow-50 border border-yellow-200';
                } else {
                    feedbackDiv.className = 'mt-6 rounded-xl p-6 transform transition-all duration-300 bg-red-50 border border-red-200';
                }
            }
        });

        console.log(`Submitted ${totalSubmitted} answers with ${errors} errors`);

        if (totalSubmitted > 0) {
            alert(`Successfully submitted ${totalSubmitted} answers!`);
        } else if (errors > 0) {
            alert(`Failed to submit answers. Please check the console for details.`);
        } else {
            alert("No answers were submitted. Please enter answers before submitting.");
        }

    } catch (error) {
        console.error('Error in submitAllAnswers:', error);
        alert('An error occurred while submitting your answers: ' + error.message);
    } finally {
        // Hide all loading indicators
        forms.forEach(form => {
            const partId = form.getAttribute('data-part-id');
            const loading = document.getElementById(`loading_${partId}`);
            if (loading) loading.classList.add('hidden');
        });
        submitAllButton.disabled = false;
    }
}

function toggleCorrectAnswer(partId) {
    const answerContent = document.getElementById(`answer_content_${partId}`);
    const arrow = document.getElementById(`arrow_${partId}`);
    const button = arrow.parentElement;

    if (answerContent.classList.contains('hidden')) {
        answerContent.classList.remove('hidden');
        arrow.classList.add('rotate-180');
        button.querySelector('span').textContent = 'Hide Correct Answer';
    } else {
        answerContent.classList.add('hidden');
        arrow.classList.remove('rotate-180');
        button.querySelector('span').textContent = 'Show Correct Answer';
    }
}

async function extractMarkingPoints(partId) {
    try {
        const response = await fetch(`/extract_marking_points/${partId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.status === 'success') {
            // Refresh the page to show new marking points
            window.location.reload();
        } else {
            alert('Error extracting marking points: ' + data.message);
        }
    } catch (error) {
        alert('Error extracting marking points: ' + error.message);
    }
}

async function updateMarkingPoint(markingPointId, field, value) {
    try {
        const response = await fetch(`/update_marking_point/${markingPointId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                [field]: value
            })
        });

        const data = await response.json();

        if (data.status !== 'success') {
            alert('Error updating marking point: ' + data.message);
        }
    } catch (error) {
        alert('Error updating marking point: ' + error.message);
    }
}

async function deleteMarkingPoint(markingPointId) {
    if (!confirm('Are you sure you want to delete this marking point?')) {
        return;
    }

    try {
        const response = await fetch(`/delete_marking_point/${markingPointId}`, {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.status === 'success') {
            // Remove the marking point element from the DOM
            const element = document.querySelector(`[data-marking-point-id="${markingPointId}"]`);
            if (element) {
                element.remove();
            }
        } else {
            alert('Error deleting marking point: ' + data.message);
        }
    } catch (error) {
        alert('Error deleting marking point: ' + error.message);
    }
}

async function addNewMarkingPoint(partId) {
    try {
        const response = await fetch(`/add_marking_point/${partId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                description: 'New marking point',
                score: 1.0,
                order: document.querySelectorAll(`#marking-points-${partId} > div`).length
            })
        });

        const data = await response.json();

        if (data.status === 'success') {
            // Refresh the page to show the new marking point
            window.location.reload();
        } else {
            alert('Error adding marking point: ' + data.message);
        }
    } catch (error) {
        alert('Error adding marking point: ' + error.message);
    }
}

async function moveMarkingPoint(markingPointId, direction) {
    try {
        const response = await fetch(`/move_marking_point/${markingPointId}/${direction}`, {
            method: 'POST'
        });

        const data = await response.json();

        if (data.status === 'success') {
            // Refresh the page to show updated order
            window.location.reload();
        } else {
            alert('Error moving marking point: ' + data.message);
        }
    } catch (error) {
        alert('Error moving marking point: ' + error.message);
    }
}

// Add this function to generate a color for marking points
function getMarkingPointColor(index) {
    const colors = [
        'bg-green-100 text-green-800',
        'bg-blue-100 text-blue-800',
        'bg-purple-100 text-purple-800',
        'bg-yellow-100 text-yellow-800',
        'bg-indigo-100 text-indigo-800'
    ];
    return colors[index % colors.length];
}

async function submitAnswer(event, partId, questionId) {
    event.preventDefault();
    const form = event.target;
    const answerInput = form.querySelector('textarea[name="answer"]');
    const answer = answerInput.value.trim();

    if (!answer) {
        alert('Please enter an answer before submitting.');
        return false;
    }

    const submitButton = document.getElementById(`submit-part-${partId}`);
    const loading = document.getElementById(`loading_${partId}`);
    const panelsDiv = document.getElementById(`answer_panels_${partId}`);

    // Disable the submit button and show loading
    submitButton.disabled = true;
    loading.classList.remove('hidden');

    try {
        const response = await fetch(form.action, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                'answer': answer
            })
        });

        const data = await response.json();

        if (data.status === 'success') {
            // Show the panels
            panelsDiv.classList.remove('hidden');

            // Get the panels
            const userAnswerDiv = document.getElementById(`user_answer_${partId}`);
            const markingSchemeDiv = document.getElementById(`marking_scheme_${partId}`);

            // Display the marking points side by side
            userAnswerDiv.innerHTML = data.marking_points.map((mp, index) => `
                <div class="grid-row flex items-start space-x-3 p-3 rounded ${mp.achieved ? 'bg-green-50' : 'bg-red-50'} h-full marking-point-mobile">
                    <div class="flex-shrink-0 pt-1">
                        ${mp.achieved
                            ? '<svg class="h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>'
                            : '<svg class="h-5 w-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/></svg>'
                        }
                    </div>
                    <div class="flex-1">
                        ${mp.evidence ? `
                            <div class="text-sm ${mp.achieved ? 'text-green-800' : 'text-red-800'} bg-white bg-opacity-50 p-3 rounded evidence-text">
                                "${mp.evidence}"
                            </div>
                        ` : '<p class="text-sm text-red-800 italic">No relevant answer found</p>'}
                    </div>
                </div>
            `).join('');

            // Display the marking scheme with aligned points
            markingSchemeDiv.innerHTML = data.marking_points.map((mp, index) => `
                <div class="grid-row flex items-center p-3 rounded bg-gray-50 h-full">
                    <p class="text-sm text-gray-900 font-medium">[${mp.score} marks] <span class="latex-content">${mp.description}</span></p>
                </div>
            `).join('');

            // Render LaTeX in the marking points
            setTimeout(() => {
                document.querySelectorAll(`#marking_scheme_${partId} .latex-content`).forEach(element => {
                    renderMathInElement(element, {
                        delimiters: [
                            {left: '$$', right: '$$', display: true},
                            {left: '$', right: '$', display: false},
                            {left: '\\(', right: '\\)', display: false},
                            {left: '\\[', right: '\\]', display: true}
                        ],
                        throwOnError: false,
                        output: 'html'
                    });
                });
            }, 100);

            // Add script to ensure heights match
            const gridRows = document.querySelectorAll('.grid-row');
            const rows = Array.from(gridRows);
            const numPoints = data.marking_points.length;

            // Match heights for each pair of marking points
            for (let i = 0; i < numPoints; i++) {
                const leftRow = rows[i];
                const rightRow = rows[i + numPoints];
                if (leftRow && rightRow) {
                    const maxHeight = Math.max(leftRow.offsetHeight, rightRow.offsetHeight);
                    leftRow.style.height = maxHeight + 'px';
                    rightRow.style.height = maxHeight + 'px';
                }
            }

        } else {
            alert('Error: ' + data.message);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('An error occurred while submitting your answer.');
    } finally {
        // Hide loading and re-enable submit button
        loading.classList.add('hidden');
        submitButton.disabled = false;
    }

    return false;
}