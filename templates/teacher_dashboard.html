{% extends "base.html" %}

{% block title %}Teacher Dashboard{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Teacher Dashboard</h1>
                    <p class="text-gray-600 mt-1">Monitor student progress and submission analytics</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-right">
                        <p class="text-sm text-gray-500">Role</p>
                        <p class="font-semibold text-gray-900">Administrator</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <span class="text-2xl">👨‍🏫</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <span class="text-blue-600 font-semibold">👥</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Students</p>
                        <p class="text-2xl font-bold text-gray-900" id="total-users">{{ analytics_data.summary.total_users }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                            <span class="text-green-600 font-semibold">📝</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Questions Attempted</p>
                        <p class="text-2xl font-bold text-gray-900" id="total-submissions">{{ analytics_data.summary.total_questions_attempted }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <span class="text-yellow-600 font-semibold">⭐</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Points Scored</p>
                        <p class="text-2xl font-bold text-gray-900" id="overall-avg-score">{{ analytics_data.summary.total_points_scored }} pts</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Group Management Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Group Management</h3>
                    <p class="text-sm text-gray-600">Manage invite codes for your groups</p>
                </div>
                <button id="refresh-groups" class="bg-gray-100 text-gray-700 px-3 py-2 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200">
                    <span class="text-sm">🔄 Refresh</span>
                </button>
            </div>

            <div id="groups-container" class="space-y-4">
                <!-- Groups will be loaded here via JavaScript -->
                <div class="text-center py-8 text-gray-500">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-2"></div>
                    Loading groups...
                </div>
            </div>
        </div>

        <!-- Filters and Controls -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Filters & Sorting</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Subject Filter -->
                <div>
                    <label for="subject-filter" class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                    <select id="subject-filter" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="">All Subjects</option>
                        {% for subject in subjects %}
                        <option value="{{ subject.id }}">{{ subject.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Topic Filter -->
                <div>
                    <label for="topic-filter" class="block text-sm font-medium text-gray-700 mb-2">Topic</label>
                    <select id="topic-filter" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" disabled>
                        <option value="">Select a subject first</option>
                    </select>
                </div>

                <!-- Group Filter -->
                <div>
                    <label for="group-filter" class="block text-sm font-medium text-gray-700 mb-2">Group</label>
                    <select id="group-filter" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="">All Groups</option>
                        {% for group in groups %}
                        <option value="{{ group.id }}">{{ group.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Sort By -->
                <div>
                    <label for="sort-by" class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                    <select id="sort-by" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="avg_score_asc">Average Score (Low to High)</option>
                        <option value="avg_score_desc">Average Score (High to Low)</option>
                        <option value="submissions_asc">Submissions (Low to High)</option>
                        <option value="submissions_desc">Submissions (High to Low)</option>
                    </select>
                </div>

                <!-- View Mode Toggle -->
                <div>
                    <label for="view-mode" class="block text-sm font-medium text-gray-700 mb-1">View Mode</label>
                    <select id="view-mode" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="users" selected>Students</option>
                        <option value="questions">Questions</option>
                    </select>
                </div>

                <!-- Apply Button -->
                <div class="flex items-end space-x-2">
                    <button id="apply-filters" class="flex-1 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors duration-200">
                        Apply Filters
                    </button>
                    <button id="export-data" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200">
                        Export CSV
                    </button>
                </div>
            </div>
        </div>

        <!-- Student Analytics Table -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Student Analytics</h3>
                <p class="text-sm text-gray-600">Detailed submission statistics for each student</p>
            </div>
            
            <!-- Loading Indicator -->
            <div id="loading-indicator" class="hidden p-8 text-center">
                <div class="inline-flex items-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Loading analytics data...
                </div>
            </div>

            <!-- Analytics Table -->
            <div class="overflow-x-auto" id="analytics-table-container">
                <table class="min-w-full divide-y divide-gray-200" id="analytics-table">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Questions Attempted</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Points Scored</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Activity</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="analytics-table-body">
                        {% for user in analytics_data.user_analytics %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ user.username }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">{{ user.grade_level or 'Not specified' }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">{{ user.email }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ user.questions_attempted }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ user.total_points }} pts
                                    </span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">{{ user.last_submission_date or 'Never' }}</div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Questions Table -->
            <div id="questions-table-container" class="overflow-x-auto hidden">
                <table class="min-w-full divide-y divide-gray-200" id="questions-table">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Question</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Topic</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Attempts</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unique Students</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Average Score</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Difficulty</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="questions-table-body">
                        <!-- Questions will be populated via JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- Empty State -->
            <div id="empty-state" class="hidden p-8 text-center">
                <div class="text-gray-500">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No student data found</h3>
                    <p class="mt-1 text-sm text-gray-500">No students have submitted answers yet, or no data matches the selected filters.</p>
                    <div class="mt-6">
                        <div class="text-sm text-gray-600">
                            <p>Try these options:</p>
                            <ul class="mt-2 list-disc list-inside text-gray-500">
                                <li>Clear any filters that might be restricting results</li>
                                <li>Check if students have completed any submissions</li>
                                <li>Create <a href="/groups/create" class="text-indigo-600 hover:text-indigo-500">groups</a> to organize students</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Teacher Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const subjectFilter = document.getElementById('subject-filter');
    const topicFilter = document.getElementById('topic-filter');
    const groupFilter = document.getElementById('group-filter');
    const sortBy = document.getElementById('sort-by');
    const viewMode = document.getElementById('view-mode');
    const applyFiltersBtn = document.getElementById('apply-filters');
    const exportBtn = document.getElementById('export-data');
    const loadingIndicator = document.getElementById('loading-indicator');
    const analyticsTableContainer = document.getElementById('analytics-table-container');
    const questionsTableContainer = document.getElementById('questions-table-container');
    const emptyState = document.getElementById('empty-state');

    let currentData = null; // Store current data for export

    // Subject filter change handler
    subjectFilter.addEventListener('change', function() {
        const subjectId = this.value;
        
        if (subjectId) {
            // Enable topic filter and load topics
            topicFilter.disabled = false;
            loadTopics(subjectId);
        } else {
            // Disable topic filter
            topicFilter.disabled = true;
            topicFilter.innerHTML = '<option value="">Select a subject first</option>';
        }
    });

    // View mode change handler
    viewMode.addEventListener('change', function() {
        loadAnalytics();
    });

    // Apply filters button handler
    applyFiltersBtn.addEventListener('click', function() {
        loadAnalytics();
    });

    // Export button handler
    exportBtn.addEventListener('click', function() {
        if (currentData) {
            exportToCSV(currentData);
        } else {
            alert('No data to export. Please apply filters first.');
        }
    });

    // Load topics for selected subject
    function loadTopics(subjectId) {
        fetch(`/api/teacher/topics/${subjectId}`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    topicFilter.innerHTML = '<option value="">All Topics</option>';
                    data.topics.forEach(topic => {
                        const option = document.createElement('option');
                        option.value = topic.id;
                        option.textContent = topic.name;
                        topicFilter.appendChild(option);
                    });
                } else {
                    console.error('Failed to load topics:', data.message);
                }
            })
            .catch(error => {
                console.error('Error loading topics:', error);
            });
    }

    // Load analytics data with filters
    function loadAnalytics() {
        const params = new URLSearchParams();

        if (subjectFilter.value) params.append('subject_id', subjectFilter.value);
        if (topicFilter.value) params.append('topic_id', topicFilter.value);
        if (groupFilter.value) params.append('group_id', groupFilter.value);
        if (sortBy.value) params.append('sort_by', sortBy.value);
        if (viewMode.value) params.append('view_mode', viewMode.value);

        // Show loading indicator
        loadingIndicator.classList.remove('hidden');
        analyticsTableContainer.classList.add('hidden');
        questionsTableContainer.classList.add('hidden');
        emptyState.classList.add('hidden');

        fetch(`/api/teacher/analytics?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                loadingIndicator.classList.add('hidden');

                if (data.status === 'success') {
                    currentData = data.data; // Store for export

                    if (data.data.view_mode === 'questions') {
                        updateQuestionsTable(data.data);
                        updateQuestionSummaryCards(data.data.summary);
                    } else {
                        updateAnalyticsTable(data.data);
                        updateSummaryCards(data.data.summary);
                    }
                } else {
                    console.error('Failed to load analytics:', data.message);
                    showEmptyState();
                }
            })
            .catch(error => {
                console.error('Error loading analytics:', error);
                loadingIndicator.classList.add('hidden');
                showEmptyState();
            });
    }

    // Update analytics table
    function updateAnalyticsTable(data) {
        const tableBody = document.getElementById('analytics-table-body');

        // Always show students table when this function is called
        analyticsTableContainer.classList.remove('hidden');
        questionsTableContainer.classList.add('hidden');
        emptyState.classList.add('hidden');

        if (data.user_analytics.length === 0) {
            showEmptyState();
            return;
        }

        tableBody.innerHTML = '';
        
        data.user_analytics.forEach(user => {
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50';

            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">${user.username}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-500">${user.grade_level || 'Not specified'}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-500">${user.email}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">${user.questions_attempted}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            ${user.total_points} pts
                        </span>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-500">${user.last_submission_date || 'Never'}</div>
                </td>
            `;
            
            tableBody.appendChild(row);
        });
        
        analyticsTableContainer.classList.remove('hidden');
    }

    // Update summary cards
    function updateSummaryCards(summary) {
        document.getElementById('total-users').textContent = summary.total_users;
        document.getElementById('total-submissions').textContent = summary.total_questions_attempted;
        document.getElementById('overall-avg-score').textContent = summary.total_points_scored + ' pts';
    }

    // Update questions table
    function updateQuestionsTable(data) {
        const tbody = document.getElementById('questions-table-body');
        tbody.innerHTML = '';

        // Always show questions table when this function is called
        questionsTableContainer.classList.remove('hidden');
        analyticsTableContainer.classList.add('hidden');
        emptyState.classList.add('hidden');

        if (data.question_analytics && data.question_analytics.length > 0) {

            data.question_analytics.forEach(question => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';

                const difficultyClass = question.avg_score >= 80 ? 'bg-green-100 text-green-800' :
                                      question.avg_score >= 60 ? 'bg-yellow-100 text-yellow-800' :
                                      'bg-red-100 text-red-800';

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">
                            <a href="/vault/${question.question_id}" class="text-indigo-600 hover:text-indigo-900 hover:underline" target="_blank">
                                ${question.title}
                            </a>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-500">${question.topic_name}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-500">${question.source}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${question.total_attempts}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${question.unique_students}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${difficultyClass}">
                                ${question.avg_score}%
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-500">${question.difficulty_level}</div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        } else {
            showEmptyState();
        }
    }

    // Update question summary cards
    function updateQuestionSummaryCards(summary) {
        document.getElementById('total-users').textContent = summary.total_questions || 0;
        document.getElementById('total-submissions').textContent = summary.total_attempts || 0;
        document.getElementById('overall-avg-score').textContent = `${summary.avg_difficulty || 0}%`;
    }

    // Show empty state
    function showEmptyState() {
        analyticsTableContainer.classList.add('hidden');
        questionsTableContainer.classList.add('hidden');
        emptyState.classList.remove('hidden');
    }

    // Export data to CSV
    function exportToCSV(data) {
        let headers, csvContent, filename;

        if (data.view_mode === 'questions') {
            headers = ['Question', 'Topic', 'Source', 'Total Attempts', 'Unique Students', 'Average Score', 'Difficulty'];
            csvContent = [
                headers.join(','),
                ...data.question_analytics.map(question => [
                    `"${question.title}"`,
                    question.topic_name,
                    question.source,
                    question.total_attempts,
                    question.unique_students,
                    question.avg_score,
                    question.difficulty_level
                ].join(','))
            ].join('\n');
            filename = `question_analytics_${new Date().toISOString().split('T')[0]}.csv`;
        } else {
            headers = ['Username', 'Grade Level', 'Email', 'Total Submissions', 'Unique Questions', 'Average Score', 'Last Activity', 'Performance Level'];
            csvContent = [
                headers.join(','),
                ...data.user_analytics.map(user => [
                    user.username,
                    user.grade_level || 'Not specified',
                    user.email,
                    user.total_submissions,
                    user.unique_questions,
                    user.avg_score,
                    user.last_submission_date || 'Never',
                    user.performance_level || 'Unknown'
                ].join(','))
            ].join('\n');
            filename = `student_analytics_${new Date().toISOString().split('T')[0]}.csv`;
        }

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // Group Management Functions
    function loadGroups() {
        fetch('/api/teacher/groups')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    displayGroups(data.groups);
                } else {
                    console.error('Error loading groups:', data.message);
                    document.getElementById('groups-container').innerHTML =
                        '<div class="text-center py-8 text-red-500">Error loading groups</div>';
                }
            })
            .catch(error => {
                console.error('Error loading groups:', error);
                document.getElementById('groups-container').innerHTML =
                    '<div class="text-center py-8 text-red-500">Error loading groups</div>';
            });
    }

    function displayGroups(groups) {
        const container = document.getElementById('groups-container');

        if (groups.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <p class="text-lg mb-2">No groups found</p>
                    <p class="text-sm">Create a group first to manage invite codes</p>
                </div>
            `;
            return;
        }

        container.innerHTML = groups.map(group => `
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-900">${group.name}</h4>
                        <p class="text-sm text-gray-600">${group.description || 'No description'}</p>
                        <p class="text-xs text-gray-500 mt-1">${group.member_count} member(s)</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="text-right">
                            ${group.invite_code ?
                                `<div class="bg-green-50 border border-green-200 rounded-md px-3 py-2">
                                    <p class="text-xs text-green-600 font-medium">Invite Code</p>
                                    <p class="text-lg font-mono font-bold text-green-800">${group.invite_code}</p>
                                </div>` :
                                `<div class="bg-gray-50 border border-gray-200 rounded-md px-3 py-2">
                                    <p class="text-xs text-gray-500">No active invite code</p>
                                </div>`
                            }
                        </div>
                        <div class="flex flex-col space-y-2">
                            ${group.invite_code ?
                                `<button onclick="revokeInviteCode(${group.id})"
                                    class="bg-red-100 text-red-700 px-3 py-1 rounded text-xs hover:bg-red-200 transition-colors">
                                    Revoke Code
                                </button>` :
                                `<button onclick="generateInviteCode(${group.id})"
                                    class="bg-blue-100 text-blue-700 px-3 py-1 rounded text-xs hover:bg-blue-200 transition-colors">
                                    Generate Code
                                </button>`
                            }
                            ${group.invite_code ?
                                `<button onclick="copyInviteCode('${group.invite_code}')"
                                    class="bg-gray-100 text-gray-700 px-3 py-1 rounded text-xs hover:bg-gray-200 transition-colors">
                                    Copy Code
                                </button>` : ''
                            }
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    // Make functions global so they can be called from onclick attributes
    window.generateInviteCode = function(groupId) {
        fetch(`/api/teacher/groups/${groupId}/generate-invite`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showNotification(data.message, 'success');
                loadGroups(); // Refresh the groups display
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error generating invite code:', error);
            showNotification('Error generating invite code', 'error');
        });
    };

    window.revokeInviteCode = function(groupId) {
        if (!confirm('Are you sure you want to revoke this invite code? Students will no longer be able to join using this code.')) {
            return;
        }

        fetch(`/api/teacher/groups/${groupId}/revoke-invite`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showNotification(data.message, 'success');
                loadGroups(); // Refresh the groups display
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error revoking invite code:', error);
            showNotification('Error revoking invite code', 'error');
        });
    };

    window.copyInviteCode = function(code) {
        navigator.clipboard.writeText(code).then(() => {
            showNotification('Invite code copied to clipboard!', 'success');
        }).catch(err => {
            console.error('Error copying to clipboard:', err);
            showNotification('Error copying to clipboard', 'error');
        });
    };

    function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-md shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // Event listeners for group management
    document.getElementById('refresh-groups').addEventListener('click', loadGroups);

    // Load initial data
    loadGroups();
    loadAnalytics();
});
</script>
{% endblock %}
